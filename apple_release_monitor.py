#!/usr/bin/env python3
"""
Apple Developer Release Monitor
监控Apple Developer RSS并自动提取Release Notes
"""

import requests
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import time
import json
import os
from datetime import datetime
from urllib.parse import urljoin, urlparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('apple_releases.log'),
        logging.StreamHandler()
    ]
)

class AppleReleaseMonitor:
    def __init__(self):
        self.rss_url = "https://developer.apple.com/news/releases/rss/releases.rss"
        self.base_url = "https://developer.apple.com"
        self.data_file = "processed_releases.json"
        self.notes_dir = "release_notes"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 创建目录
        os.makedirs(self.notes_dir, exist_ok=True)
        
        # 加载已处理的releases
        self.processed_releases = self.load_processed_releases()
    
    def load_processed_releases(self):
        """加载已处理的releases记录"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
            except:
                return set()
        return set()
    
    def save_processed_releases(self):
        """保存已处理的releases记录"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(list(self.processed_releases), f, indent=2)
    
    def fetch_rss(self):
        """获取RSS内容"""
        try:
            response = self.session.get(self.rss_url, timeout=30)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logging.error(f"获取RSS失败: {e}")
            return None
    
    def parse_rss(self, rss_content):
        """解析RSS内容"""
        try:
            root = ET.fromstring(rss_content)
            items = []
            
            for item in root.findall('.//item'):
                title = item.find('title').text if item.find('title') is not None else ""
                link = item.find('link').text if item.find('link') is not None else ""
                guid = item.find('guid').text if item.find('guid') is not None else ""
                pub_date = item.find('pubDate').text if item.find('pubDate') is not None else ""
                
                items.append({
                    'title': title,
                    'link': link,
                    'guid': guid,
                    'pub_date': pub_date
                })
            
            return items
        except Exception as e:
            logging.error(f"解析RSS失败: {e}")
            return []
    
    def extract_release_notes_url(self, release_page_url):
        """从release页面提取release notes链接"""
        try:
            response = self.session.get(release_page_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找"View release notes"链接
            release_notes_link = soup.find('a', string=lambda text: text and 'release notes' in text.lower())
            if not release_notes_link:
                # 尝试其他可能的选择器
                release_notes_link = soup.find('a', href=lambda href: href and 'rn' in href)
            
            if release_notes_link:
                href = release_notes_link.get('href')
                if href:
                    if href.startswith('/go/?id='):
                        # 处理重定向链接
                        redirect_url = urljoin(self.base_url, href)
                        redirect_response = self.session.get(redirect_url, timeout=30, allow_redirects=True)
                        return redirect_response.url
                    else:
                        return urljoin(self.base_url, href)
            
            return None
        except Exception as e:
            logging.error(f"提取release notes链接失败 {release_page_url}: {e}")
            return None
    
    def extract_release_notes_content(self, notes_url):
        """提取release notes内容"""
        try:
            response = self.session.get(notes_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 移除不需要的元素
            for element in soup(['script', 'style', 'nav', 'header', 'footer']):
                element.decompose()
            
            # 尝试找到主要内容区域
            content_selectors = [
                'main',
                '.main-content',
                '.content',
                'article',
                '.release-notes',
                'body'
            ]
            
            content = None
            for selector in content_selectors:
                content = soup.select_one(selector)
                if content:
                    break
            
            if not content:
                content = soup
            
            # 提取文本内容
            text_content = content.get_text(separator='\n', strip=True)
            
            return text_content
        except Exception as e:
            logging.error(f"提取release notes内容失败 {notes_url}: {e}")
            return None
    
    def save_release_notes(self, title, content, notes_url):
        """保存release notes到本地文件"""
        # 创建安全的文件名
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title.replace(' ', '_')
        filename = f"{safe_title}.txt"
        filepath = os.path.join(self.notes_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"Title: {title}\n")
                f.write(f"URL: {notes_url}\n")
                f.write(f"Extracted: {datetime.now().isoformat()}\n")
                f.write("=" * 80 + "\n\n")
                f.write(content)
            
            logging.info(f"Release notes已保存: {filepath}")
            return filepath
        except Exception as e:
            logging.error(f"保存release notes失败: {e}")
            return None
    
    def process_new_releases(self):
        """处理新的releases"""
        rss_content = self.fetch_rss()
        if not rss_content:
            return
        
        items = self.parse_rss(rss_content)
        new_releases = []
        
        for item in items:
            if item['guid'] not in self.processed_releases:
                new_releases.append(item)
        
        if not new_releases:
            logging.info("没有新的releases")
            return
        
        logging.info(f"发现 {len(new_releases)} 个新releases")
        
        for item in new_releases:
            logging.info(f"处理: {item['title']}")
            
            # 提取release notes链接
            notes_url = self.extract_release_notes_url(item['link'])
            if not notes_url:
                logging.warning(f"未找到release notes链接: {item['title']}")
                continue
            
            # 提取release notes内容
            content = self.extract_release_notes_content(notes_url)
            if not content:
                logging.warning(f"未能提取release notes内容: {item['title']}")
                continue
            
            # 保存到本地
            filepath = self.save_release_notes(item['title'], content, notes_url)
            if filepath:
                self.processed_releases.add(item['guid'])
                logging.info(f"成功处理: {item['title']}")
        
        # 保存处理记录
        self.save_processed_releases()
    
    def run_once(self):
        """运行一次检查"""
        logging.info("开始检查Apple Developer releases...")
        self.process_new_releases()
        logging.info("检查完成")
    
    def run_continuous(self, interval_minutes=30):
        """持续运行监控"""
        logging.info(f"开始持续监控，检查间隔: {interval_minutes} 分钟")
        
        while True:
            try:
                self.run_once()
                time.sleep(interval_minutes * 60)
            except KeyboardInterrupt:
                logging.info("监控已停止")
                break
            except Exception as e:
                logging.error(f"监控过程中出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续

if __name__ == "__main__":
    monitor = AppleReleaseMonitor()
    
    # 可以选择运行一次或持续监控
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--once":
        monitor.run_once()
    else:
        monitor.run_continuous(interval_minutes=30)

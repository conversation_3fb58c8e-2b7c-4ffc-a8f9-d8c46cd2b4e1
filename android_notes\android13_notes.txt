Android 13 行为变更
提取时间: 2025-09-02T11:09:34.187029
来源: https://developer.android.com/about/versions/13/behavior-changes-all?hl=zh-cn
================================================================================

此页面由

Cloud Translation API

翻译。

Android Developers

基本知识

行为变更：所有应用

使用集合让一切井井有条

根据您的偏好保存内容并对其进行分类。

Android 13 平台包含一些可能会影响您的应用的行为变更。以下行为变更会影响在 Android 13 上运行的所有应用，无论采用哪种

targetSdkVersion

都不例外。

您应该测试您的应用，然后根据需要进行修改，以适当地支持这些变更。

此外，请务必查看

仅影响以 Android 13 为目标平台的应用的行为变更

列表。

性能和电池

任务管理器

图 1.

任务管理器的工作流，可允许用户停止具有持续前台服务的应用。此工作流只会出现在搭载 Android 13 或更高版本的设备上。

从 Android 13（API 级别 33）开始，用户可以通过抽屉式通知栏完成工作流，以停止具有持续前台服务的应用，如图 1 所示。此功能称为

。应用必须能够

处理这种由用户发起的停止操作

使用 JobScheduler 改进预提取作业处理

利用 JobScheduler，应用可使用

JobInfo.Builder.setPrefetch()

将特定作业标记为“预提取”作业，这意味着，理想情况下这些作业应该在应用下一次启动前提前一点运行，以提升用户体验。过去，JobScheduler 仅使用该信号让预提取作业有机会使用免费或多余的数据。

在 Android 13（API 级别 33）及更高版本中，系统会尝试确定应用下次启动的时间，并根据该估算时间来运行预提取作业。应用应尝试使用预提取作业来完成他们想要在下次应用启动前完成的任何工作。

电池资源利用率

Android 13（API 级别 33）支持系统通过以下方式来更有效地管理设备电池续航时间：

更新了有关系统何时将您的应用放入

“受限”应用待机模式存储分区

的规则。

对于您的应用在以下情况下可以执行的操作制定了新限制：用户因您应用的后台电池用量过高而将其置于

“受限”状态

在测试应用时，请务必检查以下事项：

测试您的应用在系统将其放入

时的响应方式。使用以下 Android 调试桥 (ADB) 命令将应用分配到此分桶：

adb shell am set-standby-bucket

PACKAGE_NAME

restricted

测试应用对以下常见限制的响应情况，这些限制通常适用于因后台电池用量过高而处于

的应用：

无法启动前台服务

现有的前台服务会从前台移除

不会触发闹钟

不会执行作业

使用以下 ADB 命令将应用置于“受限”状态：

adb shell cmd appops set

RUN_ANY_IN_BACKGROUND ignore

高优先级 Firebase Cloud Message (FCM) 配额

Android 13（API 级别 33）更新了

Firebase Cloud Messaging

(FCM) 配额，从而提高了针对高优先级 FCM 显示通知的高优先级 FCM 传送的可靠性。Android 13（API 级别 33）中发生了以下变更：

应用待机模式存储分区

不再决定应用可以使用多少个高优先级 FCM。

如果系统检测到应用持续发送不会生成通知的高优先级消息，现在会降低这些消息的优先级。

与以前的 Android 版本一样，超出配额的高优先级 FCM 会降级为普通优先级。为了响应 FCM 而启动

前台服务

(FGS) 时，我们建议您检查

RemoteMessage.getPriority()

的结果并确认它为

PRIORITY_HIGH

，并且/或者处理任何潜在

ForegroundServiceStartNotAllowedException

异常。

如果您的应用并非始终为了响应高优先级 FCM 而发布通知，我们建议您将这些 FCM 的优先级更改为

，这样生成通知的消息就不会降级。

隐私权

通知的运行时权限

Android 13（API 级别 33）引入了运行时

通知权限

POST_NOTIFICATIONS

。

此更改有助于用户专注于最重要的通知。

强烈建议您尽快以 Android 13 或更高版本为目标平台，以获享此功能提供的额外控制和灵活性。

详细了解

应用权限最佳实践

从剪贴板中隐藏敏感内容

如果您的应用允许用户将敏感内容（例如密码或信用卡信息）复制到剪贴板，则必须在调用

ClipboardManager#setPrimaryClip()

之前向 ClipData 的

ClipDescription

添加一个标志。添加此标志可阻止敏感内容出现在内容预览中。

所复制文本的预览（未标记敏感内容）。

所复制文本的预览（已标记敏感内容）。

如需标记敏感内容，请向

添加一个布尔型 extra。无论应用的目标 API 级别如何，所有应用都应这么做。

// When your app is compiled with the API level 33 SDK or higher

clipData

apply

description

extras

PersistableBundle

().

putBoolean

EXTRA_IS_SENSITIVE

true

// If your app is compiled with a lower SDK

"android.content.extra.IS_SENSITIVE"

如需详细了解新的剪贴板界面，请访问

复制和粘贴

功能页面。

安全性

停止使用共享用户 ID

如果您的应用使用已废弃的

android:sharedUserId

属性，并且不再依赖于该属性的功能，您可以将

android:sharedUserMaxSdkVersion

属性设置为

，如以下代码段所示：

<manifest

...>

<!--

maintain

backward

compatibility,

continue

use

"android:sharedUserId"

you

already

added

your

manifest.

-->

android:sharedUserId="

SHARED_PACKAGE_NAME

android:sharedUserMaxSdkVersion="32"

...
</manifest>

这个属性会告知系统，您的应用不再依赖于共享用户 ID。如果您的应用声明

并且首次安装在搭载 Android 13 或更高版本的设备上，则应用的行为就像您从未定义过

一样。更新后的应用仍会使用现有的共享用户 ID。

共享用户 ID 会在软件包管理器中导致具有不确定性的行为。您的应用应使用适当的通信机制（例如服务和 content provider），在共享组件之间实现互操作性。

用户体验

可以关闭前台服务通知

在搭载 Android 13 或更高版本的设备上，

用户可以默认关闭与前台服务相关联的通知

核心功能

移除了旧版语音服务实现副本

Android 13 从 Google 应用中移除了

SpeechService

实现，包括 Voice IME、

RecognitionService

基于 intent 的 API

在 Android 12 中发生了以下变更：

功能已迁移到

Google 语音服务应用

，该应用已成为默认的

提供程序。

功能已移至 Android System Intelligence 应用，以支持设备端语音识别。

为了帮助在 Android 12 上保持应用兼容性，Google 应用会使用 trampoline 将流量引导至 Google 语音服务应用。在 Android 13 中，此 trampoline 已被移除。

应用应使用设备的

默认提供程序，而不是硬编码为特定应用。

本页面上的内容和代码示例受

内容许可

部分所述许可的限制。Java 和 OpenJDK 是 Oracle 和/或其关联公司的注册商标。

最后更新时间 (UTC)：2025-08-27。

[[["易于理解","easyToUnderstand","thumb-up"],["解决了我的问题","solvedMyProblem","thumb-up"],["其他","otherUp","thumb-up"]],[["没有我需要的信息","missingTheInformationINeed","thumb-down"],["太复杂/步骤太多","tooComplicatedTooManySteps","thumb-down"],["内容需要更新","outOfDate","thumb-down"],["翻译问题","translationIssue","thumb-down"],["示例/代码问题","samplesCodeIssue","thumb-down"],["其他","otherDown","thumb-down"]],["最后更新时间 (UTC)：2025-08-27。"],[],[],null,["The Android 13 platform includes behavior changes that may affect your app. The\nfollowing behavior changes apply to *all apps* when they run on Android 13,\nregardless of `targetSdkVersion`. You should test your app and then modify it as\nneeded to support these properly, where applicable.\n\nMake sure to also review the list of [behavior changes that only affect apps\ntargeting Android 13](/about/versions/13/behavior-changes-13).\n\nPerformance and battery\n\nTask Manager **Figure 1.** Workflow for Task Manager , which allows users to stop apps that have ongoing foreground services. This workflow appears only on devices that run Android 13 or higher.\n\nStarting in Android 13 (API level 33), users can complete a workflow from the\nnotification drawer to stop apps that have ongoing foreground services, as shown\nin figure 1. This affordance is known as the\n*Task Manager* . Apps must be able to [handle this\nuser-initiated\nstopping](/guide/components/foreground-services#handle-user-initiated-stop).\n\nImprove prefetch job handling using JobScheduler\n\nJobScheduler provides a way for apps to mark specific jobs as \"prefetch\"\njobs (using [`JobInfo.Builder.setPrefetch()`](/reference/android/app/job/JobInfo.Builder#setPrefetch(boolean))), meaning that they should ideally run\nclose to, and before, the next app launch to improve user experience.\nHistorically, JobScheduler has only used the signal to let prefetch jobs\nopportunistically use free or excess data.\n\nIn Android 13 (API level 33) and higher, the system tries to\ndetermine the next time an app will be launched, and uses that estimation to run\nprefetch jobs. Apps should try to use prefetch jobs for any work that they want\nto be done prior to the next app launch.\n\nBattery Resource Utilization\n\nAndroid 13 (API level 33) provides the following ways for the system to better\nmanage device battery life:\n\n- Updated rules on when the system places your app in the [\"restricted\" App\n  Standby Bucket](/topic/performance/appstandby#restricted-bucket).\n- New limitations on the work that your app can do when the user places your app in the [\"restricted\" state](/topic/performance/background-optimization#bg-restrict) for background battery usage.\n\nAs you test your app with these changes, make sure to check the following\nthings:\n\n- Test how your app responds when the system places it in the [\"restricted\" App\n  Standby Bucket](/topic/performance/appstandby#restricted-bucket). Use the\n  following Android Debug Bridge (ADB) command to assign your app to this bucket:\n\n  ```\n  adb shell am set-standby-bucket PACKAGE_NAME restricted\n  ```\n- Test how your app responds to the following restrictions that commonly apply\n  to apps that are in a [\"restricted\" state](/topic/performance/background-optimization#bg-restrict)\n  for background battery usage:\n\n  - Can't launch foreground services\n  - Existing foreground services are removed from the foreground\n  - Alarms aren't triggered\n  - Jobs aren't executed\n\n  Use the following ADB command to place your app in this \"restricted\" state:  \n\n  ```\n  adb shell cmd appops set PACKAGE_NAME RUN_ANY_IN_BACKGROUND ignore\n  ```\n\nHigh Priority Firebase Cloud Message (FCM) Quotas\n\nAndroid 13 (API level 33) updates [Firebase Cloud Messaging](https://firebase.google.com/docs/cloud-messaging) (FCM) quotas to improve the reliability of high priority FCM delivery for apps that show notifications in response to high priority FCMs. The following has changed in Android 13 (API level 33):\n\n- [App Standby Buckets](/topic/performance/appstandby) no longer determine how many high priority FCMs an app can use.\n- System now downgrades the high priority messages if it detects an app consistently sending high-priority messages that don't result in a notification.\n\nAs in previous versions of Android, high priority FCMs that go over the quota are downgraded to normal priority. When starting [Foreground Services](/guide/components/foreground-services) (FGS) in response to an FCM, we recommend checking the result of [`RemoteMessage.getPriority()`](https://firebase.google.com/docs/reference/android/com/google/firebase/messaging/RemoteMessage#getPriority()) and to confirm it is [`PRIORITY_HIGH`](https://firebase.google.com/docs/reference/android/com/google/firebase/messaging/RemoteMessage#PRIORITY_HIGH) and/or handling any potential [`ForegroundServiceStartNotAllowedException`](/reference/android/app/ForegroundServiceStartNotAllowedException) exceptions.\n\nIf your application doesn't always post notifications in response to High Priority FCMs, we recommend that you change the priority of these FCMs to **normal** so that the messages that result in a notification don't get downgraded.\n\nPrivacy\n\nRuntime permission for notifications\n\nAndroid 13 (API level 33) introduces a runtime\n[notification permission](/guide/topics/ui/notifiers/notification-permission):\n[`POST_NOTIFICATIONS`](/reference/android/Manifest.permission#POST_NOTIFICATIONS).\nThis change helps users focus on the notifications that are most important to\nthem.\n| **Note:** Notifications related to [media sessions](/guide/topics/media-apps/working-with-a-media-session) and apps that self-manage phone calls are exempt from this behavior change.\n\nWe highly recommend that you target Android 13 or higher as soon\nas possible to gain the effects of the additional control and flexibility of\nthis feature.\n\nLearn more about\n[app permissions best practices](/training/permissions/usage-notes).\n\nHide sensitive content from clipboard\n\nIf your app allows users to copy sensitive content, such as passwords or credit\ncard information, to the clipboard, you must add a flag to ClipData's\n`ClipDescription` before calling `ClipboardManager#setPrimaryClip()`. Adding\nthis flag prevents sensitive content from appearing in the content preview.  \nCopied text preview without flagging sensitive content.  \nCopied text preview flagging sensitive content.\n\n\u003cbr /\u003e\n\nTo flag sensitive content, add a boolean extra to the `ClipDescription`. All\napps should do this, regardless of the targeted API level.  \n\n\n    // When your app is compiled with the API level 33 SDK or higher\n    clipData.apply {\n        description.extras = PersistableBundle().apply {\n            putBoolean(ClipDescription.EXTRA_IS_SENSITIVE, true)\n        }\n    }\n\n    // If your app is compiled with a lower SDK\n    clipData.apply {\n        description.extras = PersistableBundle().apply {\n            putBoolean(\"android.content.extra.IS_SENSITIVE\", true)\n        }\n    }\n\nTo learn more about the new clipboard UI, visit the\n[Copy and paste](/guide/topics/text/copy-paste#SensitiveContent) feature page.\n\nSecurity\n\nMigrate away from shared user ID\n\nIf your app uses the deprecated\n[`android:sharedUserId`](/guide/topics/manifest/manifest-element#uid) attribute\nand no longer depends on the attribute's functionality, you can set the\n[`android:sharedUserMaxSdkVersion`](/guide/topics/manifest/manifest-element#uidmaxsdk)\nattribute to `32`, as shown in the following code snippet:  \n\n```xml\n\u003cmanifest ...\u003e\n    \u003c!-- To maintain backward compatibility, continue to use\n         \"android:sharedUserId\" if you already added it to your manifest. --\u003e\n    android:sharedUserId=\"\u003cvar translate=\"no\"\u003eSHARED_PACKAGE_NAME\u003c/var\u003e\"\n    android:sharedUserMaxSdkVersion=\"32\"\n    ...\n\u003c/manifest\u003e\n```\n\nThis attribute tells the system that your app no longer relies on a shared\nuser ID. If your app declares `android:sharedUserMaxSdkVersion` and is newly\ninstalled on devices running Android 13 or higher, your app\nbehaves as if you never defined `android:sharedUserId`. Updated apps still use\nthe existing shared user ID.\n| **Caution:** If you already define the `android:sharedUserId` attribute in your manifest, don't remove it. Doing so causes app updates to fail.\n\nShared user IDs cause non-deterministic behavior within the package manager.\nYour app should instead use proper communication mechanisms, such as services\nand content providers, to facilitate interoperability between shared components.\n\nUser experience\n\nDismissible foreground service notifications\n\nOn devices that run Android 13 or higher, [users can dismiss\nnotifications associated with foreground\nservices](/guide/components/foreground-services#user-dismiss-notification) by\ndefault.\n\nCore functionality\n\nLegacy copy of speech service implementation removed\n\nAndroid 13 removes the `SpeechService` implementation---including\nVoice IME, [`RecognitionService`](/reference/android/speech/RecognitionService)\nand an [intent-based\nAPI](/reference/android/speech/RecognizerIntent#ACTION_RECOGNIZE_SPEECH)---from\nthe Google app.\n\nIn Android 12, the following changes occurred:\n\n- `SpeechService` functionalities were migrated to the [Speech Services by\n  Google\n  app](https://play.google.com/store/apps/details?id=com.google.android.tts), which became the default `SpeechService` provider.\n- `RecognitionService` functionality was moved to the Android System Intelligence app to support on-device speech recognition.\n\nTo help maintain app compatibility on Android 12, the Google app\nuses a trampoline to divert traffic to the Speech Services by Google app. In\nAndroid 13, this trampoline is removed.\n\nApps should use the device's default provider for `SpeechService`, rather than\nhard-coding a specific app."]]

[代码块] adb shell am set-standby-bucketPACKAGE_NAMErestricted

[代码块] adb shell cmd appops setPACKAGE_NAMERUN_ANY_IN_BACKGROUND ignore

[代码块] // When your app is compiled with the API level 33 SDK or higherclipData.apply{description.extras=PersistableBundle().apply{putBoolean(ClipDescription.EXTRA_IS_SENSITIVE,true)}}// If your app is compiled with a lower SDKclipData.apply{description.extras=PersistableBundle().apply{putBoolean("android.content.extra.IS_SENSITIVE",true)}}

[代码块] 32

[代码块] <manifest...><!--Tomaintainbackwardcompatibility,continuetouse"android:sharedUserId"ifyoualreadyaddedittoyourmanifest.-->android:sharedUserId="SHARED_PACKAGE_NAME"android:sharedUserMaxSdkVersion="32"...
</manifest>

[代码] adb shell am set-standby-bucketPACKAGE_NAMErestricted

[代码] adb shell cmd appops setPACKAGE_NAMERUN_ANY_IN_BACKGROUND ignore

[代码] // When your app is compiled with the API level 33 SDK or higherclipData.apply{description.extras=PersistableBundle().apply{putBoolean(ClipDescription.EXTRA_IS_SENSITIVE,true)}}// If your app is compiled with a lower SDKclipData.apply{description.extras=PersistableBundle().apply{putBoolean("android.content.extra.IS_SENSITIVE",true)}}

[代码] <manifest...><!--Tomaintainbackwardcompatibility,continuetouse"android:sharedUserId"ifyoualreadyaddedittoyourmanifest.-->android:sharedUserId="SHARED_PACKAGE_NAME"android:sharedUserMaxSdkVersion="32"...
</manifest>

[代码] 32

[命令] 测试您的应用在系统将其放入“受限”应用待机模式存储分区时的响应方式。使用以下 Android 调试桥 (ADB) 命令将应用分配到此分桶：

[命令] adb shell am set-standby-bucketPACKAGE_NAMErestricted

[命令] adb shell cmd appops setPACKAGE_NAMERUN_ANY_IN_BACKGROUND ignore

[命令] <manifest...><!--Tomaintainbackwardcompatibility,continuetouse"android:sharedUserId"ifyoualreadyaddedittoyourmanifest.-->android:sharedUserId="SHARED_PACKAGE_NAME"android:sharedUserMaxSdkVersion="32"...
</manifest>
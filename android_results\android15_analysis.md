## Android 15 行为变更速览

| 项目 | 内容 |
|---|---|
| **产品名称** | Android 15 |
| **版本号** | API 35 |
| **发布日期** | 2025-08-27（文档最后更新） |

---

## 1. 兼容性变化（破坏性变更）

| 变更点 | 是否兼容旧 App | 说明 |
|---|---|---|
| **最低可安装 SDK 版本提升到 24** | ❌ **不兼容** | 低于 24 的 APK 在 Android 15 设备上**无法安装** |
| **16 KB 页面大小支持** | ❌ **不兼容** | 只要包含原生库（.so）就必须**重新编译**，否则在 16 KB 设备上**直接崩溃** |
| **软件包停止状态逻辑收紧** | ⚠️ **行为改变** | 强制停止后系统会**清除所有 PendingIntent**，并重新发送 `ACTION_BOOT_COMPLETED` |
| **后台网络访问限制** | ⚠️ **行为改变** | 进程生命周期外发起的网络请求会**抛异常**（`UnknownHostException` 等） |
| **预测性返回动画默认开启** | ⚠️ **UI 行为改变** | 移除开发者选项开关，已启用预测返回的应用将强制使用系统动画 |
| **强制停止时 Widget 被停用** | ⚠️ **行为改变** | 用户强制停止后，Widget 会变灰且不可点击，直到下次主动启动应用 |
| **PNG 表情符号字体移除** | ⚠️ **资源缺失** | 引用 `NotoColorEmojiLegacy.ttf` 的代码将**找不到资源**，导致表情符号无法显示 |

---

## 2. 对原有 App 的具体影响

| 场景 | 可能症状 | 影响范围 |
|---|---|---|
| **老 APK（targetSdk < 24）** | 安装失败，Logcat 报 `INSTALL_FAILED_DEPRECATED_SDK_VERSION` | 所有渠道安装 |
| **含 NDK 的 APK** | 启动即崩溃（SIGBUS 等） | 使用 16 KB 页面的新设备 |
| **依赖 PendingIntent 的定时/推送逻辑** | 强制停止后闹钟、通知、广播全部失效 | 所有应用 |
| **后台同步/上传逻辑** | 进程被杀后网络请求抛异常，数据丢失 | 未使用 WorkManager / ForegroundService 的应用 |
| **自定义通知监听/OTP 读取** | 无法读取含 OTP 的通知内容 | 通知监听类 App（验证码填充、手环提醒等） |
| **自定义启动器/桌面** | 看不到私密空间应用，无法锁定/解锁 | 第三方 Launcher |
| **医疗/健康类 App** | 用户把 App 装进私密空间后，锁屏即被强制停止，无法发通知 | 医疗提醒、紧急呼叫 |
| **直接/分流音频播放** | 资源不足时旧轨道被强制失效，出现无声或报错 | 影音、投屏、游戏 |
| **Widget 提供方** | 用户“强制停止”后桌面插件变灰，点击无响应 | 天气、日历、笔记类 Widget |

---

## 3. 开发者应立即采取的行动

| 优先级 | 行动项 | 操作细节 |
|---|---|---|
| 🔴 **高** | **升级 targetSdkVersion ≥ 24** | 重新打包并发布，否则新系统无法安装 |
| 🔴 **高** | **检查并重新编译原生库** | 使用 NDK r26+ 重新编译，确保 ELF 段 16 KB 对齐；可用 Android Studio 自带检测工具 |
| 🟡 **中** | **验证强制停止场景** | 在 Android 15 真机或模拟器上：<br>1. 强制停止应用<br>2. 观察 PendingIntent、Widget、通知是否恢复<br>3. 监听 `ACTION_BOOT_COMPLETED` 重新注册任务 |
| 🟡 **中** | **网络请求生命周期感知化** | 用 `LifecycleObserver` + `WorkManager`/`前台服务` 替换裸线程/后台 Service 网络调用 |
| 🟡 **中** | **适配私密空间** | 若开发 Launcher/医疗/应用商店：<br>- Launcher：申请 `ACCESS_HIDDEN_PROFILES`，实现锁定/解锁 UI<br>- 医疗：在官网/首次启动弹窗提示“请勿将本应用放入私密空间” |
| 🟢 **低** | **移除对 PNG Emoji 字体硬编码引用** | 搜索 `NotoColorEmojiLegacy.ttf` 并删除，或改用系统文本渲染 / COLRv1 字体 |
| 🟢 **低** | **测试预测性返回动画** | 打开系统动画，检查 Fragment 转场是否异常；如有问题迁移到 Navigation Component |

---

## 4. 快速检查清单（上线前必做）

- [ ] 构建一个 targetSdk=24+ 的新版本  
- [ ] 用 Android Studio “App Inspection → Native Alignment” 确认无 16 KB 对齐错误  
- [ ] 在 16 KB 模拟器（API 35, 16 KB page）跑 Monkey 测试  
- [ ] 强制停止后验证所有核心功能（通知、Widget、定时任务）  
- [ ] 在私密空间安装并测试医疗/通知类功能  
- [ ] 打开系统录屏，确认 OTP/密码输入区域已自动遮挡  

> 如需临时安装旧 APK 做回归测试，可使用  
> `adb install --bypass-low-target-sdk-block your.apk`  
> 但**切勿**将此作为最终解决方案。
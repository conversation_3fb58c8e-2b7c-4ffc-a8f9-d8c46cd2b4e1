from watchdog.events import FileSystemEventHandler
## 监控器 监控
class FileMonitor(FileSystemEventHandler):
    def __init__(self, analyzer_manager):
        self.analyzer_manager = analyzer_manager
    def on_created(self,event):
        if event.is_directory:
            return
        print(f"文件创建: {event.src_path}")
        self.analyzer_manager.analyze(event.src_path)
    def on_modified(self, event):
        if event.is_directory:
            return
        print(f'文件 {event.src_path} 被修改')
        self.analyzer_manager.analyze(event.src_path)
# Release Notes 分析报告

**原始文件**: 20250825_151454_TestFlight_Update.txt
**分析时间**: 2025-08-25 15:55:56

---

## TestFlight / App Store Connect 2025 年 6~8 月更新速览  
（基于 2025-08-25 提取的 Release Notes）

| 项目 | 内容 |
|---|---|
| **产品名称** | TestFlight & App Store Connect |
| **最新版本** | TestFlight 3.9（2025-06-10）、App Store Connect 3.0（2025-06-09） |
| **发布日期** | 2025-08-18（最新 SDK 支持公告） |

---

### 一、兼容性变化（破坏性/潜在风险）

| 风险等级 | 变化点 | 影响范围 | 说明 |
|---|---|---|---|
| 🔴 **高** | **Enhanced Security Capability** 仍被 visionOS 禁止上传 | visionOS App | 只要启用该 Capability 或 Extension，TestFlight 会拒绝上传；iOS/iPadOS/macOS 已支持。 |
| 🟠 **中** | **Apple-Hosted Background Assets** 仍不支持外部测试 | 所有平台 | 内部测试可用，外部测试（公开/邀请）会失败。 |
| 🟠 **中** | **macOS 26 beta 设备上 TestFlight 数据加载异常** | macOS 测试机 | 旧版 TestFlight 可能无法正确拉取构建版本或反馈。 |
| 🟡 **低** | **Icon Composer 图标** 上传偶发报错 | watchOS / iOS / iPadOS | Apple 已确认问题，正在修复，不影响审核逻辑。 |
| 🟡 **低** | **watchOS 最低版本 < 6.0** 导致 iOS 侧无法安装 | watchOS Extension | 临时 workaround：把 MinOSVersion 提到 6.0+。 |

---

### 二、对现有 App 的具体影响

| 场景 | 可能症状 | 影响评估 |
|---|---|---|
| **已启用 Enhanced Security 的 visionOS App** | 上传 TestFlight 直接失败，提示不支持。 | 阻断 beta 测试流程。 |
| **使用 Apple-Hosted Background Assets 并打算做外部测试** | 选择“外部测试”时报错或无响应。 | 需改用内部测试或自建 CDN。 |
| **CI 环境仍用旧版 TestFlight（<3.9）** | macOS 26 beta 测试机拉不到构建包。 | 测试人员误以为构建丢失。 |
| **watchOS Extension 部署目标 5.x** | iOS 真机侧 TestFlight 安装失败。 | 需紧急提版本号并重新打包。 |
| **使用 Icon Composer 生成图标** | 偶发上传 422 错误。 | 可重试或改用 Asset Catalog 临时规避。 |

---

### 三、开发者行动清单

| 优先级 | 行动项 | 操作建议 |
|---|---|---|
| **立即** | 升级 TestFlight 到 3.9 | 所有内部/外部测试设备（尤其 macOS 26 beta）。 |
| **立即** | 检查 visionOS Target 的 Entitlements | 若包含 Enhanced Security，暂时移除或改用其他方案。 |
| **本周内** | 评估 Background Assets 方案 | 外部测试需改用 Self-hosted 或等待后续支持。 |
| **本周内** | 统一 watchOS Extension 的 `MinimumOSVersion` ≥ 6.0 | 在 `WKCompanionAppBundleVersion` 中同步修改。 |
| **持续** | CI/CD 脚本中加入 Xcode/SDK 版本校验 | 确保上传时使用的是 Apple 当前支持的最新 beta 组合（Xcode 26 beta 6 + SDK beta 7）。 |
| **可选** | 图标改用 Asset Catalog | 规避 Icon Composer 已知 bug，直到官方修复。 |

---

### 四、一句话总结

> 在 visionOS 上别碰 Enhanced Security，外部测试别用 Apple-Hosted Background Assets，测试机统一升级到 TestFlight 3.9，就能避开本次 Release Notes 里的全部“坑”。
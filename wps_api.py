import hashlib
import hmac
import json
import requests
import os
from datetime import datetime, timezone
from typing import Dict, List, Optional

# WPS API 配置
ACCESS_KEY = 'AK20250509HUPZKK'
SECRET_KEY = '35a763bb9c7f10759dceb399b903dc28'
BASE_URL = 'https://openapi.wps.cn'


def _get_kso1_signature(method: str, uri: str, content_type: str, kso_date: str, request_body: str) -> str:
    """生成KSO-1签名"""
    sha256_hex = ''
    if request_body:
        sha256_obj = hashlib.sha256()
        sha256_obj.update(request_body.encode())
        sha256_hex = sha256_obj.hexdigest()

    mac = hmac.new(bytes(SECRET_KEY, 'utf-8'),
                   bytes('KSO-1' + method + uri + content_type + kso_date + sha256_hex, 'utf-8'),
                   hashlib.sha256)
    return mac.hexdigest()


def kso1_sign(method: str, uri: str, content_type: str, kso_date: str, request_body: str) -> Dict[str, str]:
    """生成KSO-1认证头"""
    kso_signature = _get_kso1_signature(method, uri, content_type, kso_date, request_body)
    authorization = 'KSO-1 {}:{}'.format(ACCESS_KEY, kso_signature)
    return {
        'X-Kso-Date': kso_date,
        'X-Kso-Authorization': authorization
    }


def get_current_kso_date() -> str:
    """获取当前KSO日期格式"""
    return datetime.now(timezone.utc).strftime('%a, %d %b %Y %H:%M:%S GMT')


class WPSTeamDocManager:
    """WPS团队文档管理器 - 简洁版本"""

    def __init__(self):
        self.base_url = BASE_URL
        self.session = requests.Session()
        self.access_token = None
        self._get_app_token()

    def _get_app_token(self):
        """获取应用级access_token"""
        url = f"{self.base_url}/oauth2/token"
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        data = {
            'grant_type': 'client_credentials',
            'client_id': ACCESS_KEY,
            'client_secret': SECRET_KEY
        }

        try:
            response = self.session.post(url, headers=headers, data=data)
            if response.status_code == 200:
                result = response.json()
                self.access_token = result.get('access_token')
                print(f"✅ 获取应用级token成功，有效期: {result.get('expires_in', 0)} 秒")
            else:
                print(f"❌ 获取token失败: {response.status_code}")
        except Exception as e:
            print(f"❌ 获取token异常: {e}")

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                     files: Optional[Dict] = None) -> Dict:
        """发起API请求"""
        url = f"{self.base_url}{endpoint}"
        kso_date = get_current_kso_date()

        # 处理请求体和Content-Type
        if files:
            content_type = ''
            request_body = ''
        elif data:
            content_type = 'application/json'
            request_body = json.dumps(data, separators=(',', ':'))
        else:
            content_type = 'application/json'
            request_body = ''

        # 生成认证头
        auth_headers = kso1_sign(method.upper(), endpoint, content_type, kso_date, request_body)
        headers = {**auth_headers}

        if content_type:
            headers['Content-Type'] = content_type
        if self.access_token:
            headers['Authorization'] = f'Bearer {self.access_token}'

        try:
            if files:
                response = self.session.request(method.upper(), url, files=files, data=data, headers=headers)
            elif data:
                response = self.session.request(method.upper(), url, json=data, headers=headers)
            else:
                response = self.session.request(method.upper(), url, headers=headers)

            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {'error': str(e), 'status_code': getattr(e.response, 'status_code', None)}

    def get_document_structure(self) -> Dict:
        """查询现有文档目录结构

        Returns:
            Dict: 包含所有文档库及其文件夹结构的字典
        """
        print("📚 正在查询文档库列表...")

        # 获取文档库列表
        doclibs_result = self._make_request('GET', '/v7/doclibs?page_size=5')
        if 'error' in doclibs_result:
            return {'error': f"获取文档库失败: {doclibs_result['error']}"}

        doclibs_data = doclibs_result.get('data', {})
        doclibs = doclibs_data.get('items', [])

        if not doclibs:
            print("❌ 没有找到文档库")
            return {'doclibs': [], 'message': '没有找到文档库'}

        print(f"✅ 找到 {len(doclibs)} 个文档库")

        # 获取每个文档库的文件夹结构
        result = {'doclibs': []}
        for doclib in doclibs:
            doclib_info = {
                'id': doclib.get('id'),
                'name': doclib.get('name'),
                'description': doclib.get('description', ''),
                'folders': []
            }

            print(f"📁 正在查询文档库 '{doclib_info['name']}' 的结构...")
            folders = self._get_folder_structure(doclib_info['id'], 'root')
            doclib_info['folders'] = folders

            result['doclibs'].append(doclib_info)

        return result

    def _get_folder_structure(self, doclib_id: str, folder_id: str = 'root') -> List[Dict]:
        """递归获取文件夹结构"""
        endpoint = f'/v7/doclibs/{doclib_id}/folders'
        if folder_id != 'root':
            endpoint += f'?parent_id={folder_id}'

        result = self._make_request('GET', endpoint)
        if 'error' in result:
            return []

        items = result.get('data', {}).get('items', [])
        folders = []

        for item in items:
            if item.get('type') == 'folder':
                folder_info = {
                    'id': item.get('id'),
                    'name': item.get('name'),
                    'type': 'folder',
                    'subfolders': self._get_folder_structure(doclib_id, item.get('id'))
                }
                folders.append(folder_info)
            else:
                file_info = {
                    'id': item.get('id'),
                    'name': item.get('name'),
                    'type': 'file',
                    'size': item.get('size', 0),
                    'modified_time': item.get('modified_time', '')
                }
                folders.append(file_info)

        return folders

    def upload_file_to_path(self, doclib_name: str, folder_path: str, file_path: str) -> Dict:
        """向指定目录上传文件

        Args:
            doclib_name: 文档库名称
            folder_path: 目标文件夹路径，如 "项目文档/设计稿"，空字符串表示根目录
            file_path: 本地文件路径

        Returns:
            Dict: 上传结果
        """
        if not os.path.exists(file_path):
            return {'error': f'文件不存在: {file_path}'}

        print(f"📤 开始上传文件: {os.path.basename(file_path)}")
        print(f"📚 目标文档库: {doclib_name}")
        print(f"📁 目标路径: {folder_path if folder_path else '根目录'}")

        # 1. 查找文档库
        doclib = self._find_doclib_by_name(doclib_name)
        if not doclib:
            return {'error': f'未找到名为 "{doclib_name}" 的文档库'}

        doclib_id = doclib['id']

        # 2. 确保文件夹路径存在
        folder_id = self._ensure_folder_path(doclib_id, folder_path)
        if not folder_id:
            return {'error': f'无法创建或找到文件夹路径: {folder_path}'}

        # 3. 上传文件
        file_name = os.path.basename(file_path)
        with open(file_path, 'rb') as f:
            files = {'file': (file_name, f, 'application/octet-stream')}
            data = {'folder_id': folder_id}
            result = self._make_request('POST', f'/v7/doclibs/{doclib_id}/files', data, files)

        if 'error' in result:
            return result

        print(f"✅ 文件上传成功!")
        return result

    def _find_doclib_by_name(self, name: str) -> Optional[Dict]:
        """根据名称查找文档库"""
        doclibs_result = self._make_request('GET', '/v7/doclibs?page_size=50')
        if 'error' in doclibs_result:
            return None

        doclibs = doclibs_result.get('data', {}).get('items', [])
        for doclib in doclibs:
            if doclib.get('name') == name:
                return doclib
        return None

    def _ensure_folder_path(self, doclib_id: str, folder_path: str) -> Optional[str]:
        """确保文件夹路径存在，如果不存在则创建"""
        if not folder_path or folder_path == '/':
            return 'root'

        path_parts = [part.strip() for part in folder_path.split('/') if part.strip()]
        current_folder_id = 'root'

        for folder_name in path_parts:
            # 查找当前层级是否已存在该文件夹
            endpoint = f'/v7/doclibs/{doclib_id}/folders'
            if current_folder_id != 'root':
                endpoint += f'?parent_id={current_folder_id}'

            result = self._make_request('GET', endpoint)
            if 'error' in result:
                return None

            items = result.get('data', {}).get('items', [])
            found_folder = None

            for item in items:
                if item.get('type') == 'folder' and item.get('name') == folder_name:
                    found_folder = item
                    break

            if found_folder:
                current_folder_id = found_folder['id']
            else:
                # 创建文件夹
                data = {'name': folder_name, 'parent_id': current_folder_id}
                create_result = self._make_request('POST', f'/v7/doclibs/{doclib_id}/folders', data)
                if 'error' in create_result:
                    print(f"❌ 创建文件夹 '{folder_name}' 失败: {create_result['error']}")
                    return None
                current_folder_id = create_result.get('data', {}).get('id')
                if not current_folder_id:
                    return None
                print(f"✅ 创建文件夹: {folder_name}")

        return current_folder_id

    def get_oauth_url(self, redirect_uri: str, scope: str = 'user_info') -> str:
        """获取OAuth2.0授权URL

        Args:
            redirect_uri: 回调地址
            scope: 权限范围，默认为'user_info'

        Returns:
            授权URL
        """
        import urllib.parse

        params = {
            'client_id': ACCESS_KEY,
            'redirect_uri': redirect_uri,
            'response_type': 'code',
            'scope': scope
        }

        query_string = urllib.parse.urlencode(params)
        return f"{self.base_url}/oauth/v1/authorize?{query_string}"

    def get_access_token(self, code: str, redirect_uri: str) -> Dict:
        """通过授权码获取access_token (用户授权方式)

        Args:
            code: 授权码
            redirect_uri: 回调地址

        Returns:
            包含access_token的响应
        """
        data = {
            'client_id': ACCESS_KEY,
            'client_secret': SECRET_KEY,
            'code': code,
            'redirect_uri': redirect_uri,
            'grant_type': 'authorization_code'
        }

        response = self.session.post(f"{self.base_url}/oauth/v1/token", data=data)
        try:
            return response.json()
        except:
            return {'error': 'Failed to parse response', 'response_text': response.text}

    def get_app_access_token(self) -> Dict:
        """获取应用级access_token (client_credentials方式)

        这是应用级认证，不需要用户授权，适合服务端使用

        Returns:
            包含access_token的响应
        """
        url = f"{self.base_url}/oauth2/token"

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        data = {
            'grant_type': 'client_credentials',
            'client_id': ACCESS_KEY,
            'client_secret': SECRET_KEY
        }

        try:
            print(f"🔑 正在获取应用级access_token...")
            print(f"📡 请求URL: {url}")
            print(f"📋 请求数据: grant_type=client_credentials&client_id={ACCESS_KEY}&client_secret={SECRET_KEY[:10]}...")

            response = self.session.post(url, headers=headers, data=data)

            print(f"📊 响应状态码: {response.status_code}")
            print(f"📄 响应头: {dict(response.headers)}")

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功获取应用级access_token!")
                print(f"🔑 Token: {result.get('access_token', '')[:30]}...")
                print(f"⏰ 有效期: {result.get('expires_in', 0)} 秒")
                return result
            else:
                error_result = {
                    'error': f'HTTP {response.status_code}',
                    'response_text': response.text,
                    'status_code': response.status_code
                }
                print(f"❌ 获取失败: {error_result}")
                return error_result

        except Exception as e:
            error_result = {'error': str(e)}
            print(f"❌ 请求异常: {error_result}")
            return error_result

    def get_app_access_token(self) -> Dict:
        """获取应用级access_token (client_credentials方式)

        这是应用级认证，不需要用户授权，适合服务端使用

        Returns:
            包含access_token的响应
        """
        url = f"{self.base_url}/oauth2/token"

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        data = {
            'grant_type': 'client_credentials',
            'client_id': ACCESS_KEY,
            'client_secret': SECRET_KEY
        }

        try:
            print(f"🔑 正在获取应用级access_token...")
            print(f"📡 请求URL: {url}")
            print(f"📋 请求数据: grant_type=client_credentials&client_id={ACCESS_KEY}&client_secret={SECRET_KEY[:10]}...")

            response = self.session.post(url, headers=headers, data=data)

            print(f"📊 响应状态码: {response.status_code}")
            print(f"📄 响应头: {dict(response.headers)}")

            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功获取应用级access_token!")
                print(f"🔑 Token: {result.get('access_token', '')[:30]}...")
                print(f"⏰ 有效期: {result.get('expires_in', 0)} 秒")
                return result
            else:
                error_result = {
                    'error': f'HTTP {response.status_code}',
                    'response_text': response.text,
                    'status_code': response.status_code
                }
                print(f"❌ 获取失败: {error_result}")
                return error_result

        except Exception as e:
            error_result = {'error': str(e)}
            print(f"❌ 请求异常: {error_result}")
            return error_result

    def set_access_token(self, access_token: str):
        """设置access_token"""
        self.access_token = access_token

    def get_doclibs(self, page_size: int = 50, page_token: str = None) -> Dict:
        """获取团队文档库列表

        Args:
            page_size: 分页大小，默认50
            page_token: 分页token，用于获取下一页
        """
        # 构建查询参数
        params = {'page_size': page_size}
        if page_token:
            params['page_token'] = page_token

        # 构建完整的endpoint
        query_string = '&'.join([f'{k}={v}' for k, v in params.items()])
        endpoint = f'/v7/doclibs?{query_string}'

        return self._make_request('GET', endpoint)

    def get_doclib_info(self, doclib_id: str) -> Dict:
        """获取指定文档库信息"""
        return self._make_request('GET', f'/v7/doclibs/{doclib_id}')

    def get_doclib_folders(self, doclib_id: str, parent_id: str = 'root') -> Dict:
        """获取文档库中的文件夹和文件列表

        Args:
            doclib_id: 文档库ID
            parent_id: 父文件夹ID，默认为'root'表示根目录
        """
        endpoint = f'/v7/doclibs/{doclib_id}/folders'
        if parent_id != 'root':
            endpoint += f'?parent_id={parent_id}'
        return self._make_request('GET', endpoint)

    def create_folder(self, doclib_id: str, folder_name: str, parent_id: str = 'root') -> Dict:
        """在文档库中创建文件夹

        Args:
            doclib_id: 文档库ID
            folder_name: 文件夹名称
            parent_id: 父文件夹ID，默认为'root'表示根目录
        """
        data = {
            'name': folder_name,
            'parent_id': parent_id
        }
        return self._make_request('POST', f'/v7/doclibs/{doclib_id}/folders', data)

    def upload_file(self, doclib_id: str, file_path: str, folder_id: str = 'root',
                   file_name: Optional[str] = None) -> Dict:
        """上传文件到文档库

        Args:
            doclib_id: 文档库ID
            file_path: 本地文件路径
            folder_id: 目标文件夹ID，默认为'root'表示根目录
            file_name: 自定义文件名，如果不提供则使用原文件名
        """
        if not os.path.exists(file_path):
            return {'error': f'文件不存在: {file_path}'}

        if file_name is None:
            file_name = os.path.basename(file_path)

        with open(file_path, 'rb') as f:
            files = {'file': (file_name, f, 'application/octet-stream')}
            data = {'folder_id': folder_id}
            return self._make_request('POST', f'/v7/doclibs/{doclib_id}/files', data, files)

    def search_files(self, doclib_id: str, keyword: str, folder_id: Optional[str] = None) -> Dict:
        """在文档库中搜索文件

        Args:
            doclib_id: 文档库ID
            keyword: 搜索关键词
            folder_id: 限制搜索范围的文件夹ID，如果不提供则搜索整个文档库
        """
        params = {'q': keyword}
        if folder_id:
            params['folder_id'] = folder_id

        endpoint = f'/v7/doclibs/{doclib_id}/search'
        query_string = '&'.join([f'{k}={v}' for k, v in params.items()])
        endpoint += f'?{query_string}'

        return self._make_request('GET', endpoint)

    def delete_file(self, doclib_id: str, file_id: str) -> Dict:
        """删除文档库中的文件

        Args:
            doclib_id: 文档库ID
            file_id: 文件ID
        """
        return self._make_request('DELETE', f'/v7/doclibs/{doclib_id}/files/{file_id}')

    def delete_folder(self, doclib_id: str, folder_id: str) -> Dict:
        """删除文档库中的文件夹

        Args:
            doclib_id: 文档库ID
            folder_id: 文件夹ID
        """
        return self._make_request('DELETE', f'/v7/doclibs/{doclib_id}/folders/{folder_id}')

    def get_file_info(self, doclib_id: str, file_id: str) -> Dict:
        """获取文件详细信息

        Args:
            doclib_id: 文档库ID
            file_id: 文件ID
        """
        return self._make_request('GET', f'/v7/doclibs/{doclib_id}/files/{file_id}')





def main():
    """简洁版本示例用法"""
    print("🚀 WPS团队文档API - 简洁版本")
    print("=" * 50)

    # 创建管理器
    manager = WPSTeamDocManager()

    if not manager.access_token:
        print("❌ 无法获取access_token，请检查配置")
        return
    
    # 1. 查询文档目录结构
    print("\n📁 1. 查询文档目录结构")
    print("-" * 30)
    structure = manager.get_document_structure()

    if 'error' in structure:
        print(f"❌ 查询失败: {structure['error']}")
    else:
        print("✅ 查询成功!")
        for doclib in structure['doclibs']:
            print(f"📚 文档库: {doclib['name']}")
            for item in doclib['folders']:
                if item['type'] == 'folder':
                    print(f"  📁 {item['name']}")
                else:
                    print(f"  📄 {item['name']}")

    # 2. 上传文件示例说明
    print(f"\n📤 2. 文件上传示例")
    print("-" * 30)
    print("使用方法:")
    print("result = manager.upload_file_to_path(")
    print("    doclib_name='文档库名称',")
    print("    folder_path='文件夹路径',")
    print("    file_path='本地文件路径'")
    print(")")

    print(f"\n🎯 完成!")



if __name__ == '__main__':


    # 然后运行主程序
    main()


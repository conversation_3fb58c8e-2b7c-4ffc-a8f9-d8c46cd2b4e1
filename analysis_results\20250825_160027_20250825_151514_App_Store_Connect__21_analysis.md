# Release Notes 分析报告

**原始文件**: 20250825_151514_App_Store_Connect__21.txt
**分析时间**: 2025-08-25 16:00:27

---

## App Store Connect 2.1 版本分析报告  
（基于 2025-08-25 提取的 Release Notes）

---

### 1. 版本信息
| 项目 | 内容 |
|---|---|
| **产品名称** | App Store Connect（含 TestFlight） |
| **版本号** | 2.1（App Store Connect 网页端）<br>3.9（TestFlight App） |
| **发布日期** | 2025-08-18（最新更新） |

---

### 2. 兼容性变化

| 类别 | 变化说明 | 是否破坏性 |
|---|---|---|
| **SDK 支持** | 新增 Xcode 26 beta 6 + iOS / iPadOS / macOS / tvOS / visionOS / watchOS 26 beta 7 的构建上传 | ❌ 非破坏性（仅 Beta 阶段） |
| **功能限制** | 1. Apple-Hosted Background Assets **仍不支持外部测试**（所有平台）<br>2. visionOS **仍不支持** Enhanced Security capability / extension | ✅ 若 App 依赖这两项功能，则无法对外测试或上架 visionOS |
| **已知问题** | TestFlight 在 macOS 26 beta 上可能无法加载数据 | ✅ 影响内部/外部测试体验 |

---

### 3. 对原有 App 的具体影响

| 场景 | 影响描述 |
|---|---|
| **已在 TestFlight 进行外部测试** | 如果使用了 Apple-Hosted Background Assets 或 visionOS Enhanced Security，**外部测试会被拒绝上传**。 |
| **使用 Icon Composer 生成图标** | watchOS / iOS / iPadOS App 在上传时可能报「图标错误」，需等待 Apple 修复。 |
| **watchOS Extension** | 若 watch App 的 `MinimumOSVersion < 6.0`，会导致 iOS 侧无法安装，**必须手动提升到 ≥ 6.0**。 |
| **Background Assets 测试** | 1. 内部测试已支持 Apple-Hosted，但外部测试仍被禁止。<br>2. Self-hosted Managed Background Assets 在 Beta 阶段存在安装/下载卡顿、状态回调丢失等已知 Bug，需要额外处理。 |
| **TestFlight for macOS 26 beta** | 测试人员需先升级到 TestFlight 3.9，否则可能无法加载测试数据。 |

---

### 4. 开发者推荐行动清单

| 优先级 | 行动项 | 备注 |
|---|---|---|
| 🔴 **高** | 检查并移除 **Apple-Hosted Background Assets** 在外部测试中的使用，或改用 Self-hosted 方案 | 否则外部测试无法上传 |
| 🔴 **高** | 若面向 visionOS，**暂时移除 Enhanced Security capability / extension** | 等待后续 Beta 支持 |
| 🟡 **中** | 将 **watch App 的 MinimumOSVersion 提升至 6.0+** | 避免 iOS 侧安装失败 |
| 🟡 **中** | 在 Background Assets 相关代码中加入 **下载超时 / 取消 / 重试逻辑** | 规避 Beta 已知 Bug |
| 🟢 **低** | 提醒内部测试团队：macOS 26 beta 设备 **务必升级 TestFlight 至 3.9** | 减少“无法加载测试数据”的误报 |
| 🟢 **低** | 若使用 Icon Composer 图标，**准备备用图标或等待 Apple 修复** | 避免上传被卡 |

---

### 一句话总结  
本次更新主要是 **Beta SDK 支持范围扩大**，但 **Apple-Hosted Background Assets 与 visionOS Enhanced Security 依旧是外部测试/上架的“拦路虎”**；开发者应优先排查这两项功能的使用，并针对 watchOS 6.0 以下部署版本做强制升级。
#!/usr/bin/env python3
"""
AI Release Notes Analyzer
使用LangChain和AI模型分析Apple Developer Release Notes
"""

import os
import json
import time
import yaml
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import hashlib
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# LangChain imports
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_community.llms import Ollama
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 处理环境变量
            self._process_env_vars(config)
            return config
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _process_env_vars(self, config: Dict[str, Any]):
        """处理配置中的环境变量"""
        def process_value(value):
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                env_var = value[2:-1]
                return os.getenv(env_var, "")
            elif isinstance(value, dict):
                for k, v in value.items():
                    value[k] = process_value(v)
            elif isinstance(value, list):
                return [process_value(item) for item in value]
            return value
        
        for key, value in config.items():
            config[key] = process_value(value)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "ai_analysis": {
                "api": {
                    "provider": "openai",
                    "model": "gpt-4o-mini",
                    "temperature": 0.3,
                    "max_tokens": 2000
                }
            },
            "monitoring": {
                "watch_directory": "release_notes",
                "check_interval": 30
            }
        }

class AIAnalyzer:
    """AI分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.llm = self._create_llm()
        self.prompt_template = self._create_prompt_template()
    
    def _create_llm(self):
        """创建LLM实例"""
        api_config = self.config["ai_analysis"]["api"]
        provider = api_config["provider"].lower()
        
        common_params = {
            "model": api_config["model"],
            "temperature": api_config.get("temperature", 0.3),
            "max_tokens": api_config.get("max_tokens", 2000),
            "timeout": api_config.get("timeout", 60)
        }
        
        if provider == "openai":
            params = {
                "api_key": api_config.get("api_key"),
                **common_params
            }
            if api_config.get("base_url"):
                params["base_url"] = api_config["base_url"]
            return ChatOpenAI(**params)
        
        elif provider == "anthropic":
            return ChatAnthropic(
                api_key=api_config.get("api_key"),
                **common_params
            )
        
        elif provider == "ollama":
            return Ollama(
                model=api_config["model"],
                base_url=api_config.get("base_url", "http://localhost:11434")
            )
        
        else:
            raise ValueError(f"不支持的AI提供商: {provider}")
    
    def _create_prompt_template(self):
        """创建提示词模板"""
        prompts = self.config["ai_analysis"]["prompts"]
        
        return ChatPromptTemplate.from_messages([
            ("system", prompts["system_prompt"]),
            ("human", prompts["user_prompt_template"])
        ])
    
    def analyze_content(self, filename: str, content: str) -> Optional[str]:
        """分析内容"""
        try:
            # 检查文件大小限制
            max_size = self.config.get("advanced", {}).get("max_file_size", 1048576)
            if len(content.encode('utf-8')) > max_size:
                logging.warning(f"文件 {filename} 超过大小限制，跳过分析")
                return None
            
            # 格式化提示词
            messages = self.prompt_template.format_messages(
                filename=filename,
                content=content[:10000]  # 限制内容长度
            )
            
            # 调用AI分析
            response = self.llm.invoke(messages)
            return response.content
        
        except Exception as e:
            logging.error(f"AI分析失败 {filename}: {e}")
            return None

class FileMonitor(FileSystemEventHandler):
    """文件监控器"""
    
    def __init__(self, analyzer_manager):
        self.analyzer_manager = analyzer_manager
    
    def on_created(self, event):
        """文件创建事件"""
        if not event.is_directory:
            self.analyzer_manager.process_file(event.src_path)
    
    def on_modified(self, event):
        """文件修改事件"""
        if not event.is_directory:
            self.analyzer_manager.process_file(event.src_path)

class AnalyzerManager:
    """分析管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.config
        self.ai_analyzer = AIAnalyzer(self.config)
        
        # 设置日志
        self._setup_logging()
        
        # 创建必要目录
        self._create_directories()
        
        # 加载处理记录
        self.processed_files = self._load_processed_files()
    
    def _setup_logging(self):
        """设置日志"""
        log_config = self.config.get("logging", {})
        
        handlers = []
        if log_config.get("console", True):
            handlers.append(logging.StreamHandler())
        
        if log_config.get("file"):
            handlers.append(logging.FileHandler(log_config["file"], encoding='utf-8'))
        
        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", "%(asctime)s - %(levelname)s - %(message)s"),
            handlers=handlers,
            force=True
        )
    
    def _create_directories(self):
        """创建必要目录"""
        output_config = self.config["ai_analysis"]["output"]
        analysis_dir = output_config.get("analysis_dir", "analysis_results")
        Path(analysis_dir).mkdir(exist_ok=True)
    
    def _load_processed_files(self) -> Dict[str, str]:
        """加载已处理文件记录"""
        db_path = self.config["monitoring"].get("processed_files_db", "processed_analysis.json")
        try:
            if os.path.exists(db_path):
                with open(db_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logging.error(f"加载处理记录失败: {e}")
        return {}
    
    def _save_processed_files(self):
        """保存已处理文件记录"""
        db_path = self.config["monitoring"].get("processed_files_db", "processed_analysis.json")
        try:
            with open(db_path, 'w', encoding='utf-8') as f:
                json.dump(self.processed_files, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存处理记录失败: {e}")
    
    def _get_file_hash(self, filepath: str) -> str:
        """获取文件哈希值"""
        try:
            with open(filepath, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def _should_process_file(self, filepath: str) -> bool:
        """判断是否应该处理文件"""
        # 检查文件扩展名
        file_patterns = self.config["monitoring"].get("file_patterns", ["*.txt"])
        if not any(Path(filepath).match(pattern) for pattern in file_patterns):
            return False
        
        # 检查忽略模式
        ignore_patterns = self.config["monitoring"].get("ignore_patterns", [])
        if any(Path(filepath).match(pattern) for pattern in ignore_patterns):
            return False
        
        # 检查是否已处理
        file_hash = self._get_file_hash(filepath)
        if filepath in self.processed_files and self.processed_files[filepath] == file_hash:
            return False
        
        return True
    
    def _save_analysis_result(self, filename: str, analysis: str) -> str:
        """保存分析结果"""
        output_config = self.config["ai_analysis"]["output"]
        analysis_dir = output_config.get("analysis_dir", "analysis_results")
        
        # 生成输出文件名
        base_name = Path(filename).stem
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if output_config.get("include_timestamp", True):
            output_name = f"{timestamp}_{base_name}_analysis"
        else:
            output_name = f"{base_name}_analysis"
        
        # 确定文件格式
        format_type = output_config.get("format", "markdown")
        if format_type == "markdown":
            ext = ".md"
        elif format_type == "json":
            ext = ".json"
        else:
            ext = ".txt"
        
        output_path = Path(analysis_dir) / f"{output_name}{ext}"
        
        # 准备输出内容
        if format_type == "json":
            output_content = json.dumps({
                "original_file": filename,
                "analysis_time": datetime.now().isoformat(),
                "analysis": analysis
            }, indent=2, ensure_ascii=False)
        else:
            header = f"# Release Notes 分析报告\n\n"
            if output_config.get("include_original_filename", True):
                header += f"**原始文件**: {filename}\n"
            header += f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            header += "---\n\n"
            output_content = header + analysis
        
        # 保存文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(output_content)
            logging.info(f"分析结果已保存: {output_path}")
            return str(output_path)
        except Exception as e:
            logging.error(f"保存分析结果失败: {e}")
            return ""
    
    def process_file(self, filepath: str):
        """处理单个文件"""
        if not self._should_process_file(filepath):
            return
        
        logging.info(f"开始分析文件: {filepath}")
        
        try:
            # 读取文件内容
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # AI分析
            analysis = self.ai_analyzer.analyze_content(Path(filepath).name, content)
            if not analysis:
                logging.warning(f"分析失败: {filepath}")
                return
            
            # 保存分析结果
            if self.config["ai_analysis"]["output"].get("save_analysis", True):
                self._save_analysis_result(Path(filepath).name, analysis)
            
            # 更新处理记录
            file_hash = self._get_file_hash(filepath)
            self.processed_files[filepath] = file_hash
            self._save_processed_files()
            
            logging.info(f"文件分析完成: {filepath}")
        
        except Exception as e:
            logging.error(f"处理文件失败 {filepath}: {e}")
    
    def scan_existing_files(self):
        """扫描现有文件"""
        watch_dir = self.config["monitoring"]["watch_directory"]
        if not os.path.exists(watch_dir):
            logging.warning(f"监控目录不存在: {watch_dir}")
            return
        
        logging.info(f"扫描现有文件: {watch_dir}")
        
        for root, dirs, files in os.walk(watch_dir):
            for file in files:
                filepath = os.path.join(root, file)
                self.process_file(filepath)
    
    def start_monitoring(self):
        """开始监控"""
        watch_dir = self.config["monitoring"]["watch_directory"]
        
        # 先扫描现有文件
        self.scan_existing_files()
        
        # 设置文件监控
        event_handler = FileMonitor(self)
        observer = Observer()
        observer.schedule(event_handler, watch_dir, recursive=True)
        
        logging.info(f"开始监控目录: {watch_dir}")
        observer.start()
        
        try:
            while True:
                time.sleep(self.config["monitoring"].get("check_interval", 30))
        except KeyboardInterrupt:
            logging.info("停止监控")
            observer.stop()
        
        observer.join()

if __name__ == "__main__":
    import sys
    
    config_path = "config.yaml"
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
    
    analyzer = AnalyzerManager(config_path)
    
    if len(sys.argv) > 2 and sys.argv[2] == "--scan-only":
        analyzer.scan_existing_files()
    else:
        analyzer.start_monitoring()

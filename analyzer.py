import os
import yaml
import time
import logging
import hashlib

from watchdog.observers import <PERSON>
from watchdog.events import FileSystemEventHandler

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate

## 配置读取器
class config_reader:
    def __init__(self, config_file):
        self.config_file = config_file
        self.config = self.read_config()
    def _read_config(self):
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError :
            print(f"读取配置不存在: {self.config_file}")
            return {}
        except yaml.YAMLError as e:
            print(f"YAML解析错误: {e}")
            return {}

## AI分析器 只做分析
class AIAnalyzer:
    def __init__(self, config):
        self.config = config
        self.api_config = self.config['ai_analysis']['api']
        self.prompt_template = self._create_prompt_template()
        self.llm = self.create_llm()
        
        self._create_directories()
    def _create_llm(self):
        self.api_config.pop('provider')
        return ChatOpenAI(**self.api_config)

    def _create_prompt_template(self):
        prompts = self.config["ai_analysis"]["prompts"]
        
        return ChatPromptTemplate.from_messages([
            ("system", prompts["system_prompt"]),
            ("human", prompts["user_prompt_template"])
        ])

    def analyze_content(self, content):
        max_size = self.config.get("advanced", {}).get("max_file_size", 1048576)
        if len(content.encode('utf-8')) > max_size:
            print(f"文件超过大小限制，跳过分析")
            return None
        messages = self.prompt_template.format_messages(
            content=content[:10000]  # 限制内容长度
        )

        return self.llm.invoke(messages).content
    
## 监控器 监控
class FileMonitor(FileSystemEventHandler):
    def on_created(self,event):
        if event.is_directory:
            return
        print(f"文件创建: {event.src_path}")
        self.analyzer_manager.process_file(event.src_path)
    def on_modified(self, event):
        if event.is_directory:
            return
        print(f'文件 {event.src_path} 被修改')
        self.analyzer_manager.process_file(event.src_path)


## 分析管理器 log 调度 文件夹控制
class AnalysisManager:
    def __init__(self,config):
        self._setup_logging()
        self.config_manager = config_reader(config)
        self.config = self.config_manager.config
        self.ai_analyzer = AIAnalyzer(self.config)
        self.event_handler = FileMonitor(self)
        self.observer = Observer()
        self._create_directories()
        self.output_config = self.config['ai_analysis']['output']
        self.output_dir = self.output_config.get("analysis_dir")
        
    # 创建必要目录
    def _create_directories(self):
        analysis_dir = self.output_dir
        os.makedirs(analysis_dir, exist_ok=True)

    ## 分析并保存
    def analyze(self,filepath):
        name = os.path.basename(filepath)
        logging.info(f"开始分析文件: {name}")
        processed_files = self._load_processed_files()

        if(name in processed_files):
            logging.info(f"文件已处理: {name}")
            return

        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            analysis = self.ai_analyzer.analyze_content(content)
        if analysis:
            open(os.path.join(self.output_dir, name + '.md'), 'w', encoding='utf-8').write(analysis)
            self._save_processed_files(name)
        else:
            print(f"分析失败: {filepath}")

    ## 启动监控
    def start_monitoring(self):
        watch_dir = self.config["monitoring"]["watch_directory"]
        self.observer.schedule(self.event_handler, watch_dir, recursive=True)
        self.observer.start()
        try:
            while True:
                time.sleep(self.config["monitoring"].get("check_interval", 30))
        except KeyboardInterrupt:
            self.observer.stop()
            logging.info("停止监控")
        self.observer.join()

    ## log记录
    def _setup_logging(self):
        log_config = self.config.get("logging")

        handlers = []
        if log_config.get("console", True):
            handlers.append(logging.StreamHandler())
        
        if log_config.get("file"):
            handlers.append(logging.FileHandler(log_config["file"], encoding='utf-8'))
        
        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", "%(asctime)s - %(levelname)s - %(message)s"),
            handlers=handlers,
            force=True
        )
        
    ## 保存已经处理的文件
    def _save_processed_files(self,filename):
        with open("processed_files.txt", 'a') as f:
            f.write(filename + '\n')

    ## 加载已经处理的文件
    def _load_processed_files(self):
        if os.path.exists("processed_files.txt"):
            with open("processed_files.txt", 'r') as f:
                return set(f.read().splitlines())
        return set()
    


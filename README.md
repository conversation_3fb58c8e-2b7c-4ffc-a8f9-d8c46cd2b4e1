# Apple Developer Release Monitor & AI Analyzer

自动监控Apple Developer RSS并使用AI分析Release Notes的完整解决方案。

## 功能特性

### RSS监控模块 (apple_release_monitor.py)
- 🔄 自动监控Apple Developer RSS更新
- 📄 自动提取Release Notes内容
- 💾 本地保存Release Notes文件
- 🚫 避免重复处理已处理的releases
- 📝 详细的日志记录
- ⚡ 高效的网络请求处理

### AI分析模块 (ai_release_analyzer.py)
- 🤖 基于LangChain的AI分析框架
- 📊 自动分析Release Notes内容
- 🔧 支持多种AI模型 (OpenAI, Anthropic, Ollama)
- ⚙️ 完全可配置的提示词和参数
- 📁 实时监控新文件并自动分析
- 💾 多格式输出 (Markdown, JSON, TXT)

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置AI API
编辑 `config.yaml` 文件，设置你的API密钥：
```yaml
ai_analysis:
  api:
    provider: "openai"  # 或 anthropic, ollama
    model: "gpt-4o-mini"
    api_key: "your-api-key-here"  # 或设置环境变量 OPENAI_API_KEY
```

### 3. 运行方式

#### 方式一：同时运行RSS监控和AI分析
```bash
python run_analysis.py
```

#### 方式二：分别运行

**RSS监控 - 运行一次检查**
```bash
python apple_release_monitor.py --once
```

**RSS监控 - 持续监控（默认30分钟检查一次）**
```bash
python apple_release_monitor.py
```

**AI分析 - 扫描现有文件**
```bash
python ai_release_analyzer.py --scan-only
```

**AI分析 - 持续监控新文件**
```bash
python ai_release_analyzer.py
```

## 输出文件

### RSS监控输出
- `release_notes/` - Release Notes文件保存目录
- `processed_releases.json` - 已处理的releases记录
- `apple_releases.log` - RSS监控日志

### AI分析输出
- `analysis_results/` - AI分析结果保存目录
- `processed_analysis.json` - 已分析文件记录
- `ai_analysis.log` - AI分析日志

## 文件命名规则

Release Notes文件命名格式：`YYYYMMDD_HHMMSS_产品名称.txt`

例如：`20250825_143022_Xcode_26_beta_6.txt`

## 配置说明

所有配置都在 `config.yaml` 文件中，支持以下配置：

### AI分析配置
- **API设置**：支持OpenAI、Anthropic、Ollama等多种AI提供商
- **模型参数**：temperature、max_tokens、timeout等
- **提示词**：完全自定义的系统提示词和用户提示词模板
- **输出格式**：支持Markdown、JSON、TXT格式

### 监控配置
- **监控目录**：指定要监控的目录
- **文件过滤**：支持文件模式匹配和忽略规则
- **检查间隔**：自定义检查频率

### 高级配置
- **并发处理**：控制同时分析的文件数量
- **重试机制**：网络请求失败时的重试策略
- **文件大小限制**：避免处理过大的文件
- **缓存机制**：避免重复分析相同内容

## 程序特点

- **最小化修改**：只处理新的releases和文件，避免重复工作
- **高效写法**：使用session复用连接，异步处理，合理的错误处理
- **可读性强**：清晰的类结构和函数命名，详细的注释
- **完全可配置**：所有参数都可通过配置文件调整，无需修改代码

## 工作流程

### 完整流程
1. **RSS监控**：定期检查Apple Developer RSS
2. **内容提取**：访问新release页面，提取Release Notes
3. **文件保存**：将Release Notes保存到本地
4. **AI分析**：监控新文件，自动进行AI分析
5. **结果输出**：生成结构化的分析报告

### RSS监控流程
1. 获取RSS内容
2. 解析新的release项目
3. 访问release页面提取Release Notes链接
4. 下载并提取Release Notes内容
5. 保存到本地文件
6. 记录已处理的releases避免重复

### AI分析流程
1. 监控 `release_notes/` 目录
2. 检测新文件或文件变化
3. 读取文件内容
4. 调用AI模型进行分析
5. 生成结构化分析报告
6. 保存分析结果到 `analysis_results/`

## 示例分析输出

AI分析会提供以下结构化信息：
- **版本信息**：产品名称、版本号、发布日期
- **重要更新**：3-5个最重要的新功能或改进
- **开发者影响**：这些更新对开发者的具体影响
- **兼容性变化**：破坏性变化或兼容性问题
- **推荐行动**：开发者应该采取的行动建议

## 注意事项

- 程序会自动处理Apple的重定向链接
- 支持多种Release Notes页面格式
- 网络请求包含适当的超时和重试机制
- AI分析支持多种模型，可根据需要选择
- 所有配置都可通过 `config.yaml` 文件调整

Title: TestFlight Update
URL: https://developer.apple.com/help/app-store-connect/release-notes/
Extracted: 2025-08-25T15:15:04.409090
================================================================================

App Store Connect Help
Support
/
App Store Connect
/
Release notes
Filter
⌘K
Filter
⌘K
Release notes
Release Notes
Learn about new features and updates in App Store Connect.
August 18, 2025
You can now upload apps built with Xcode 26 beta 6 using the SDK for iOS 26 beta 7, iPadOS 26 beta 7, macOS 26 beta 7, tvOS 26 beta 7, visionOS 26 beta 7, and watchOS 26 beta 7 for internal and external testing.
Not yet supported:
Apple-Hosted Background Assets for external testing on all platforms.
Apps built with the Enhanced Security capability or Enhanced Security extensions on visionOS.
Known issues:
TestFlight may not load data properly on devices using macOS 26 beta. To prevent this issue, we recommend updating to TestFlight version 3.9.
TestFlight Xcode
August 6, 2025
You can now upload apps built with Xcode 26 beta 5 using the SDK for iOS 26 beta 5, iPadOS 26 beta 5, macOS 26 beta 5, tvOS 26 beta 5, visionOS 26 beta 5, and watchOS 26 beta 5 for internal and external testing.
Not yet supported:
Apple-Hosted Background Assets for external testing on all platforms.
Apps built with the Enhanced Security capability or Enhanced Security extensions on visionOS.
Known issues:
TestFlight may not load data properly on devices using macOS 26 beta. To prevent this issue, we recommend updating to TestFlight version 3.9.
TestFlight Xcode
July 22, 2025
You can now upload apps built with Xcode 26 beta 4 using the SDK for iOS 26 beta 4, iPadOS 26 beta 4, macOS 26 beta 4, tvOS 26 beta 4, visionOS 26 beta 4, and watchOS 26 beta 4 for internal and external testing , with support for the following:
Apps built with the Enhanced Security capability or Enhanced Security extensions on iOS, iPadOS, and macOS.
Not yet supported:
Apple-Hosted Background Assets for external testing on all platforms.
Apps built with the Enhanced Security capability or Enhanced Security extensions on visionOS.
Known issues:
TestFlight may not load data properly on devices using macOS 26 beta. To prevent this issue, we recommend updating to TestFlight version 3.9.
If you’ve added an icon made with Icon Composer as part of your watchOS app submission, you may encounter an icon related error upon upload. We are aware of this issue and working to resolve it.
TestFlight Xcode
July 8, 2025
You can now upload apps built with Xcode 26 beta 3 using the SDK for iOS 26 beta 3, iPadOS 26 beta 3, macOS 26 beta 3, tvOS 26 beta 3, visionOS 26 beta 3, and watchOS 26 beta 3 for internal and external testing, with support for the following:
Icon Composer icons on iOS, iPadOS, and macOS
iphone-performance-gaming-tier apps on visionOS
Not yet supported:
Apple-Hosted Background Assets for external testing on all platforms.
Apps built with the Enhanced Security capability or Enhanced Security extensions.
Known issues:
Installation status may not be reflected correctly in the TestFlight app on the macOS 26 beta.
TestFlight may not load data properly on devices using macOS 26 beta. To prevent this issue, we recommend updating to TestFlight version 3.9.
If you’ve added an icon made with Icon Composer as part your iOS or watchOS app submission, you may encounter an icon related error upon upload. We are aware of this issue and working to resolve it.
iOS apps with an watchOS extension where the watch app has a minimum deployment target earlier than watchOS 6.0 will fail to install on iOS. We are working to resolve this, in the meantime, the workaround is to change the MinimumOSVersion in the watch app to 6.0 or later.
TestFlight Xcode
June 24, 2025
You can now submit apps built with Xcode 26 beta 2 using the SDK for iOS 26 beta 2, iPadOS 26 beta 2, macOS 26 beta 2, tvOS 26 beta 2, visionOS 26 beta 2, and watchOS 26 beta 2 for internal and external testing, with support for the following:
Apple-Hosted Background Assets for internal testing on iOS, iPadOS, and macOS, tvOS, and visionOS
Not yet supported:
Apple-Hosted Background Assets for external testing on all platforms.
Apps built with the Enhanced Security capability or Enhanced Security extensions.
Known issues:
Installation status may not be reflected correctly in the TestFlight app on the macOS 26 beta.
TestFlight may not load data properly on devices using macOS 26 beta. To prevent this issue, we recommend updating to TestFlight version 3.9.
If you’ve added an icon made with Icon Composer as part of your watchOS app submission, you may encounter an icon related error upon upload. We are aware of this issue and working to resolve it.
Self-hosted Managed Background Assets on iOS, iPadOS, macOS, tvOS, and visionOS
An iOS or iPadOS beta app on a Mac with Apple silicon might appear as openable in TestFlight while its essential assets are still being downloaded. Wait a few minutes to give time for the essential assets to finish being downloaded before attempting to open the app.
Pausing and resuming a beta app installation or update while the system is downloading essential asset packs could cause the installation or update to stall indefinitely. As a workaround, delete the stalled beta app from the Home Screen (on iOS, iPadOS, or tvOS), from the Home View (on visionOS), or from the Finder (on macOS) and reinstall it.
The system might not deliver status updates to your beta app for ongoing asset-pack downloads. As a workaround, force-quit and restart your beta app while testing it.
Downloads of asset-packs with prefetch download policies might stall. Cancel the download in your app’s code to resolve this.
Apple-Hosted Background Assets installation of large asset packs may fail.
TestFlight Xcode
June 10, 2025
TestFlight 3.9 is now available on the App Store for iPhone, iPad, Mac, and Apple Vision Pro. This update includes:
Introduction of Apple-Hosted Background Assets for testing.
Faster installation of macOS apps on Apple Silicon.
Stability improvements and bug fixes.
TestFlight
June 9, 2025
App Store Connect 3.0 is now available on the App Store for iPhone and iPad. With this update you can:
View TestFlight screenshot and crash feedback.
Receive push notifications for TestFlight feedback.
Enjoy stability improvements and bug fixes.
App Store Connect for iPhone and iPad
June 9, 2025
You can now submit apps built with Xcode 26 beta using the SDK for iOS 26 beta , iPadOS 26 beta, macOS 26 beta, tvOS 26 beta, visionOS 26 beta, and watchOS 26 beta for internal and external testing, with support for the following:
Apple-Hosted Background Assets for internal testing on iOS, iPadOS, and macOS
Self-hosted Managed Background Assets on iOS, iPadOS, macOS, tvOS, and visionOS
FinanceKit Transaction Picker on iOS
Carrier Messaging Application on iOS
Hardened Process on iOS, iPadOS, macOS, tvOS, and visionOS
Accessory Setup Extension on iOS, iPadOS
Low Latency Streaming on visionOS
WiFi Aware on iOS
iCloud Extended Share Access on iOS, iPadOS, macOS, tvOS, and visionOS
Background GPU Access on iOS
Hotspot Provider on iOS and visionOS
Device Discovery Pairing Access on iOS
Default Dialer App on iOS
Network Extensions on iOS and macOS
Declared Age on iOS and macOS
Wireless Insights Service Predictions on iOS
EnergyKit on iOS and macOS
Digital Credentials API - Mobile Document Provider on iOS
Carrier-Constrained Network - Category & Optimized on iOS
ID Verifier - Data Transfer on iOS
Not yet supported:
Apple-Hosted Background Assets for internal testing on visionOS and tvOS or external testing on all platforms.
Apps built with the Enhanced Security capability or Enhanced Security extensions.
Known issues:
Installation status may not be reflected correctly in the TestFlight app on the macOS 26 beta.
TestFlight may not load data properly on devices using macOS 26 beta. To prevent this issue, we recommend updating to TestFlight version 3.9.
If you’ve added an icon made with Icon Composer as part your app submission, you may encounter an icon related error upon upload. We are aware of this issue and working to resolve it.
Self-hosted Managed Background Assets on iOS, iPadOS, macOS, tvOS, and visionOS
An iOS or iPadOS beta app on a Mac with Apple silicon might appear as openable in TestFlight while its essential assets are still being downloaded. Wait a few minutes to give time for the essential assets to finish being downloaded before attempting to open the app.
Pausing and resuming a beta app installation or update while the system is downloading essential asset packs could cause the installation or update to stall indefinitely. As a workaround, delete the stalled beta app from the Home Screen (on iOS, iPadOS, or tvOS), from the Home View (on visionOS), or from the Finder (on macOS) and reinstall it.
The system might not deliver status updates to your beta app for ongoing asset-pack downloads. As a workaround, force-quit and restart your beta app while testing it.
Downloads of asset-packs with prefetch download policies might stall. Cancel the download in your app’s code to resolve this.
TestFlight Xcode
May 15, 2025
You can now submit apps built with Xcode 16.4 RC using the SDKs for iOS 18.5 RC, iPadOS 18.5 RC, macOS 15.5 RC, tvOS 18.5 RC, visionOS 2.5 RC, and watchOS 11.5 RC for the App Store, and for internal and external testing through TestFlight.
TestFlight
April 28, 2025
You can now submit apps built with Xcode 16.4 beta using the SDK for iOS 18.5 beta 4, iPadOS 18.5 beta 4, macOS 15.5 beta 4, tvOS 18.5 beta 4, visionOS 2.5 beta 4, and watchOS 11.5 beta 4 for internal and external testing.
Xcode TestFlight
April 15, 2025
TestFlight 3.8.1 is now available on the App Store for iPhone, iPad, Mac, Apple TV, and Apple Vision Pro. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
April 15, 2025
You can now publish up to 10 In-App Events on the App Store at a time. Additionally, you can have up to 15 approved In-App Events per app in App Store Connect at a time.
In-App Event
Overview of In-App Events
March 25, 2025
You can now submit apps built with Xcode 16.3 RC using the SDKs for iOS 18.4 RC, iPadOS 18.4 RC, macOS 15.4 RC, tvOS 18.4 RC, visionOS 2.4 RC, and watchOS 11.4 RC for the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
March 21, 2025
TestFlight 3.8 is now available on the App Store for iPhone, iPad, Mac, Apple TV, and Apple Vision Pro. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
March 18, 2025
You can now submit apps built with Xcode 16.3 beta 3 using the SDK for iOS 18.4 beta 4, iPadOS 18.4 beta 4, macOS 15.4 beta 4, tvOS 18.4 beta 4, visionOS 2.4 beta 4, and watchOS 11.4 beta 4 for internal and external testing.
Xcode TestFlight
January 28, 2025
App Store Connect 2.1 is now available on the App Store for iPhone and iPad. This update includes:
Dark mode icon support.
Resolved issue in Ratings and Reviews to show reviews for macOS-only apps. (FB15786900)
Resolved issue with sharing marketing videos for certain apps. (FB16019377)
Resolved issue in Trends to show details for Finance and Sales users.
Additional stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
January 18, 2025
TestFlight 3.7.1 is now available on the App Store for iPhone, iPad, Mac, Apple TV, and Apple  Vision Pro. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
December 9, 2024
TestFlight 3.7 is now available on the App Store for iPhone, iPad, Mac, and Apple Vision Pro. This update includes:
Dark mode icon support for TestFlight on iOS and iPadOS.
New language support on visionOS: Italian, Spanish, and Spanish (Latin America).
Stability improvements and bug fixes on all platforms.
TestFlight App Store Connect for iPhone and iPad
December 5, 2024
You can now submit apps built with Xcode 16.2 RC using the SDKs for iOS 18.2 RC, iPadOS 18.2 RC, macOS 15.2  RC, tvOS 18.2 RC, visionOS 2.2 RC, and watchOS 11.2 RC for the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
November 21, 2024
You can now submit apps built with Xcode 16.2 beta 3 using the SDK for iOS 18.2 beta 4, iPadOS 18.2 beta 4, macOS 15.2 beta 4, tvOS 18.2 beta 3, visionOS 2.2 beta 3, and watchOS 11.2 beta 3 for internal and external testing.
Xcode TestFlight
November 20, 2024
App Store Connect 2.0.1 is now available on the App Store. This update includes stability improvements and bug fixes.
App Store Connect for iPhone and iPad
November 12, 2024
App Store Connect 2.0 is now available on the App Store. This update includes a refreshed UI and the ability to generate marketing assets and easily share them on social media channels, enabling you to promote key moments like new app launches, version updates, or when your app is featured in select placements on the Today tab in the App Store. You can also receive notifications when your app gets featured in select placements on the Today tab — for example as App of the Day or Game of the Day.
Additionally, this update includes stability improvements and bug fixes.
App Store Connect for iPhone and iPad
November 4, 2024
You can now submit apps built with Xcode 16.2 beta 2 using the SDK for iOS 18.2 beta 2, iPadOS 18.2 beta 2, macOS 15.2 beta 2, tvOS 18.2 beta, visionOS 2.2 beta, and watchOS 11.2 beta for internal and external testing.
Xcode TestFlight
October 24, 2024
TestFlight 3.6 is now available on the App Store for iPhone, iPad, Mac, and Apple Vision Pro. This update includes several key improvements, including enhancements to the invitation experience, the introduction of tester criteria, and the addition of new public link metrics for better insights.
Additionally, this update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
October 21, 2024
You can now submit apps built with Xcode 16.1 RC using the SDKs for iOS 18.1 RC, iPadOS 18.1 RC, macOS 15.1 RC, tvOS 18.1 RC, visionOS 2.1 RC, and watchOS 11.1 RC for the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
October 10, 2024
Starting today, the Sales and Trends tool in App Store Connect offers a downloadable win-back eligibility report, which can help you estimate the number of subscriptions eligible for a win-back offer. Existing subscription metrics and reports have also been updated to include win-back offer data, enabling you to better measure the performance of specific offers available in your app.
Sales and Trends
Win-back eligibility report
Download and view reports
September 17, 2024
You can now submit apps built with Xcode 16.1 beta 2 using the SDK for iOS 18.1 beta 4, iPadOS 18.1 beta 4, macOS 15.1 beta 4, tvOS 18.1 beta, visionOS 2.1 beta, and watchOS 11.1 beta for internal and external testing.
Xcode TestFlight
September 17, 2024
You can now submit apps built with Xcode 16 using the SDKs for iOS 18, iPadOS 18, macOS 15, tvOS 18, visionOS 2, and watchOS 11 for the App Store, and for internal and external testing through TestFlight.
Note:
Due to an issue causing apps to crash on launch on an OS version earlier than macOS 15, iOS 18, tvOS 18, watchOS 11, or visionOS 2, you cannot submit apps built with Xcode 16RC.
Xcode TestFlight
September 11, 2024
Starting today, when
uploading app screenshots
, you’re only required to provide a single screenshot (6.5" or 6.9") for iPhone and single screenshot (13") for iPad with your app submission. If you prefer, you can still provide screenshots for any iPhone and iPad display sizes that aren’t required.
September 9, 2024
You can now submit apps built with Xcode 16 Release Candidate using the SDKs for iOS 18 RC, iPadOS 18 RC, macOS 15 RC, tvOS 18 RC, visionOS 2 RC, and watchOS 11 RC for the App Store, and for internal and external testing through TestFlight, with support for the following:
Spatial Audio Profile on iOS, iPadOS, macOS, tvOS, and visionOS
Head Pose on iOS, iPadOS, macOS, and tvOS
FSKit Module on macOS
Format Reader and Video Decoder media extensions on macOS
RAW Video Processor media extension on macOS
Xcode TestFlight
August 20, 2024
You can now submit apps built with Xcode 16 beta 6 using the SDK for iOS 18 beta 6, iPadOS 18 beta 6, macOS 15 beta 6, tvOS 18 beta 6 visionOS 2 beta 6, and watchOS 11 beta 6 for internal and external testing, with support for the following:
Spatial Audio Profile on iOS, iPadOS, macOS, tvOS, and visionOS
Head Pose on iOS, iPadOS, macOS, and tvOS
FSKit Module on macOS
Format Reader and Video Decoder media extensions on macOS
RAW Video Processor media extension on macOS
Xcode TestFlight
August 6, 2024
You can now submit apps built with Xcode 16 beta 5 using the SDK for iOS 18 beta 5, iPadOS 18 beta 5, macOS 15 beta 5, tvOS 18 beta 5 visionOS 2 beta 5, and watchOS 11 beta 5 for internal and external testing, with support for the following:
Spatial Audio Profile on iOS, iPadOS, macOS, tvOS, and visionOS
Head Pose on iOS, iPadOS, macOS, and tvOS
FSKit Module on macOS
Format Reader and Video Decoder media extensions on macOS
RAW Video Processor media extension on macOS
Known issue:
If you’ve added dark or tinted icons for your app and the light icon is not appearing on the device Home Screen, ensure that the light icon has been added to the app.
Xcode TestFlight
July 23, 2024
You can now submit apps built with Xcode 16 beta 4 using the SDK for iOS 18 beta 4, iPadOS 18 beta 4, macOS 15 beta 4, tvOS 18 beta 4 visionOS 2 beta 4, and watchOS 11 beta 4 for internal and external testing, with support for the following:
Spatial Audio Profile on iOS, iPadOS, macOS, tvOS, and visionOS
Head Pose on iOS, iPadOS, macOS, and tvOS
FSKit Module on macOS
Format Reader and Video Decoder media extensions on macOS
RAW Video Processor media extension on macOS
Known issue:
If you’ve added dark or tinted icons for your app and the light icon is not appearing on the device Home Screen, ensure that the light icon has been added to the app.
Xcode TestFlight
July 17, 2024
Starting today, App Store Connect no longer supports
iCloud
display sets. You can use a single entry in iCloud user settings and share other data among your apps by configuring them to use a single
CloudKit container
.
iCloud display set
July 9, 2024
You can now submit apps built with Xcode 16 beta 3 using the SDK for iOS 18 beta 3, iPadOS 18 beta 3, macOS 15 beta 3, tvOS 18 beta 3, visionOS 2 beta 3, and watchOS 11 beta 3 for internal and external testing, with support for the following:
Spatial Audio Profile on iOS, iPadOS, macOS, tvOS, and visionOS
Head Pose on iOS, iPadOS, macOS, and tvOS
FSKit Module on macOS
Format Reader and Video Decoder media extensions on macOS
RAW Video Processor media extension on macOS
Known issue:
If you’ve added dark or tinted icons for your app and the light icon is not appearing on the device Home Screen, ensure that the light icon has been added to the app.
Xcode TestFlight
June 24, 2024
You can now submit apps built with Xcode 16 beta 2 using the SDK for iOS 18 beta 2, iPadOS 18 beta 2, macOS 15 beta 2, tvOS 18 beta 2, visionOS 2 beta 2, and watchOS 11 beta 2 for internal and external testing, with support for the following:
Spatial Audio Profile on iOS, iPadOS, macOS, tvOS, and visionOS
Head Pose on iOS, iPadOS, macOS, and tvOS
FSKit Module on macOS
Format Reader and Video Decoder media extensions on macOS
RAW Video Processor media extension on macOS
Known issue:
If you’ve added dark or tinted icons for your app and the light icon is not appearing on the device Home Screen, ensure that the light icon has been added to the app.
Xcode TestFlight
June 20, 2024
TestFlight 3.5.2 is now available on the App Store for iPhone, iPad, Mac, Apple TV, and Apple Vision Pro. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
TestFlight on the App Store
June 10, 2024
You can now submit apps built with Xcode 16 beta using the SDK for iOS 18 beta, iPadOS 18 beta, macOS 15 beta, tvOS 18 beta, visionOS 2 beta, and watchOS 11 beta for internal and external testing, with support for the following:
Spatial Audio Profile on iOS, iPadOS, macOS, tvOS, and visionOS
Head Pose on iOS, iPadOS, macOS, and tvOS
FSKit Module on macOS
Format Reader and Video Decoder media extensions on macOS
Not yet supported:
RAW Video Processor media extension on macOS
Known issues:
iPhone and iPad apps on Apple Vision Pro may be installed on your Home Screen instead of the Compatible Apps folder.
If you’ve added dark or tinted icons for your app and the light icon is not appearing on the device Home Screen, ensure that the light icon has been added to the app.
Xcode TestFlight
May 9, 2024
You can now submit apps built with Xcode 15.4 RC using the SDK for iOS 17.5 RC, iPadOS 17.5 RC, macOS 14.5 RC, tvOS 17.5 RC, and watchOS 10.5 RC for the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
April 16, 2024
You can now submit apps built with Xcode 15.4 beta using the SDK for iOS 17.5 beta 2, iPadOS 17.5 beta 2, macOS 14.5 beta 2, tvOS 17.5 beta 2, visionOS 1.2 beta 2, and watchOS 10.5 beta 2 for internal and external testing.
Xcode TestFlight
March 5, 2024
TestFlight 3.5.1 is now available on the App Store for iPhone, iPad, Mac, Apple TV, and Apple Vision Pro. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
March 5, 2024
You can now submit apps built with Xcode 15.3 using the SDK for iOS 17.4, iPadOS 17.4, macOS 14.4 Release Candidate, tvOS 17.4 Release Candidate, visionOS 1.1 Release Candidate, and watchOS 10.4 Release Candidate for the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
February 28, 2024
Payments and proceeds data is now available sooner each fiscal month in Payments and Financial Reports. Starting the first Wednesday: Data starts to appear for the previous fiscal month.
By the first Friday: Data for the previous fiscal month continues to populate until reports are ready for all regions.
You can
sign up to be notified
by email as soon as your reports are available.
Payments and Financial report
Fiscal Year 2024 Accounting Calendar
February 27, 2024
You can now submit apps built with Xcode 15.3 Release Candidate using the SDK for iOS 17.4 Release Candidate and iPadOS 17.4 Release Candidate for the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
February 22, 2024
App Analytics now provides more than a dozen metrics for visionOS apps, including data about user acquisition, engagement, and monetization. Data is available for apps built for visionOS, as well as existing iPadOS and iOS apps running on Apple Vision Pro.
App Analytics
February 13, 2024
You can now submit apps built with Xcode 15.3 beta 3 using the SDK for iOS 17.4 beta 3, iPadOS 17.4 beta 3, macOS 14.4 beta 3, tvOS 17.4 beta 3, visionOS 1.1 beta 2, and watchOS 10.4 beta 3 for internal and external testing.
Xcode TestFlight
February 8, 2024
This update includes
changes to app statuses
:
Ready for Sale has been renamed to Ready for Distribution, and now indicates that your app can be published. To view if your app is available on the App Store, visit Pricing and Availability.
Developer Removed from Sale, Removed from Sale, and Pending Agreement are no longer app statuses.
This update also provides support for features related to
alternative app distribution in the European Union
. You can now:
Add alternative app marketplaces.
Generate alternative distribution packages.
Turn on notifications to automatically inform alternative app marketplaces about package updates.
Submit iOS apps for Notarization.
February 8, 2024
TestFlight 3.4.4 is now available on the App Store for iPhone and iPad. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
February 8, 2024
App Store Connect 1.12.4 is now available on the App Store. This update includes stability improvements and bug fixes.
App Store Connect for iPhone and iPad
February 7, 2024
You can now submit apps built with Xcode 15.3 beta 2 using the SDK for iOS 17.4 beta 2, iPadOS 17.4 beta 2, macOS 14.4 beta 2, tvOS 17.4 beta 2, visionOS 1.1 beta, and watchOS 10.4 beta 2 for internal and external testing.
Xcode TestFlight
February 7, 2024
You can now filter to view metrics from visionOS app transactions in Sales and Trends.
Sales and Trends
January 8, 2024
You can now submit apps built with Xcode 15.2 using the SDK for iOS 17.2, iPadOS 17.2, macOS 14.2, tvOS 17.2, visionOS 1, and watchOS 10.2 for the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
January 8, 2024
TestFlight 3.5 is now available on the App Store for Apple Vision Pro, supporting beta testing of visionOS apps, as well as compatible iPadOS and iOS apps. Testers can easily send app screenshots of issues they encounter and share additional context if an app crashes.
To learn more about using TestFlight for visionOS, watch
App Store Connect for spatial computing
.
TestFlight App Store Connect for iPhone and iPad
December 12, 2023
You can now submit apps built with Xcode 15.2 beta using the SDK for iOS 17.2, iPadOS 17.2, macOS 14.2, tvOS 17.2, visionOS 1 beta 7, and watchOS 10.2 for internal and external testing.
Xcode TestFlight
December 5, 2023
You can now submit apps built with Xcode 15.1 Release Candidate using the SDK for iOS 17.2 Release Candidate, iPadOS 17.2 Release Candidate, macOS 14.2 Release Candidate, tvOS 17.2 Release Candidate, and watchOS 10.2 Release Candidate for the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
November 30, 2023
App Store Connect 1.12.3 is now available on the App Store. This update includes stability improvements and bug fixes.
App Store Connect for iPhone and iPad
November 14, 2023
You can now submit apps built with Xcode 15.1 beta 3 using the SDK for iOS 17.2 beta 3, iPadOS 17.2 beta 3, macOS 14.2 beta 3, tvOS 17.2 beta 3, visionOS 1 beta 6, and watchOS 10.2 beta 3 for internal and external testing.
Xcode TestFlight
November 10, 2023
The Apple sandbox environment lets you test in-app purchases on devices using the product information that you set up in App Store Connect. Now you can change a test account’s storefront, adjust subscription renewal rates, clear purchase history, and simulate interrupted purchase flows directly on your iPhone or iPad, in addition to in App Store Connect. You can also test Family Sharing using Sandbox Test Families in App Store Connect, allowing testers to share access to auto-renewable subscriptions and non-consumables with up to five test family members.
In-App Purchases Sandbox
Testing In-App Purchases with sandbox
Manage Sandbox Apple Account settings
October 26, 2023
You can now submit apps built with Xcode 15.1 beta 2 using the SDK for iOS 17.2 beta, iPadOS 17.2 beta, macOS 14.2 beta, tvOS 17.2 beta, visionOS 1 beta 5, and watchOS 10.2 beta for internal and external testing.
Xcode TestFlight
October 24, 2023
TestFlight 3.4.3 is now available on the App Store for iPhone and iPad. This update includes stability improvements and bug fixes. Specifically, an issue preventing users from accepting an invitation to test a beta app via redemption code was resolved. Testers on iOS 17.1, iPadOS 17.1, or later will need to use this latest version of TestFlight in order to accept an invitation via redemption code.
TestFlight App Store Connect for iPhone and iPad
October 3, 2023
You can now submit apps built with Xcode 15.1 beta using the SDK for visionOS beta 4 for internal and external testing.
Xcode TestFlight
September 13, 2023
TestFlight 3.4.2 is now available on the App Store for iPhone and iPad. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
September 12, 2023
You can now submit apps built with Xcode 15 RC using the SDK for iOS 17 RC, iPadOS 17 RC, macOS 14 RC, tvOS 17 RC, and watchOS 10 RC to the App Store, and for internal and external testing through TestFlight.
Apps using the visionOS beta 3 SDK can continue to be built with Xcode 15 beta 8 for internal and external testing through TestFlight.
Xcode TestFlight
August 29, 2023
You can now submit apps built with Xcode 15 beta 8 using the SDK for iOS 17 beta 8, iPadOS 17 beta 8, macOS 14 beta 6, tvOS 17 beta 8, visionOS beta 3, and watchOS 10 beta 8 for internal and external testing, with support for the following:
Tap to Present ID on iOS
Apple Pay Later Merchandising on iOS and iPadOS
Shallow Depth and Pressure on watchOS
VMNet on macOS
Sensitive Content Analysis on iOS, iPadOS, and macOS
In addition, TestFlight Internal Only distribution from Xcode is now supported.
Xcode TestFlight
August 23, 2023
You can now submit apps built with Xcode 15 beta 7 using the SDK for iOS 17 beta 7, iPadOS 17 beta 7, macOS 14 beta 6, tvOS 17 beta 7, visionOS beta 2, and watchOS 10 beta 7 for internal and external testing, with support for the following:
Tap to Present ID on iOS
Apple Pay Later Merchandising on iOS and iPadOS
Shallow Depth and Pressure on watchOS
VMNet on macOS
Sensitive Content Analysis on iOS, iPadOS, and macOS
In addition, TestFlight Internal Only distribution from Xcode is now supported.
Xcode TestFlight
August 9, 2023
You can now submit apps built with Xcode 15 beta 6 using the SDK for iOS 17 beta 5, iPadOS ;17 beta 5, macOS 14 beta 5, tvOS 17 beta 5, visionOS beta 2, and watchOS 10 beta 5 for internal and external testing, with support for the following:
Tap to Present ID on iOS
Apple Pay Later Merchandising on iOS and iPadOS
Shallow Depth and Pressure on watchOS
VMNet on macOS
Sensitive Content Analysis on iOS, iPadOS, and macOS
In addition, TestFlight Internal Only distribution from Xcode is now supported.
Xcode TestFlight
August 7, 2023
App Store Connect 1.12.2 is now available on the App Store. This update includes stability improvements and bug fixes.
App Store Connect for iPhone and iPad
August 2, 2023
You can now submit apps built with Xcode 15 beta 5 using the SDK for iOS 17 beta 4, iPadOS 17 beta 4, macOS 14 beta 4, tvOS 17 beta 4, visionOS beta 2, and watchOS 10 beta 4 for internal and external testing, with support for the following:
Tap to Present ID on iOS
Apple Pay Later Merchandising on iOS and iPadOS
Shallow Depth and Pressure on watchOS
Sensitive Content Analysis on iOS, iPadOS, and macOS
In addition, TestFlight Internal Only distribution from Xcode is now supported.
Xcode TestFlight
July 12, 2023
You can now submit apps built with Xcode 15 beta 4 using the SDK for iOS 17 beta 3, iPadOS 17 beta 3, visionOS 1 beta, macOS 14 beta 3, tvOS 17 beta 3, and watchOS 10 beta 3 for internal and external testing, with support for the following:
Tap to Present ID on iOS
Apple Pay Later Merchandising on iOS and iPadOS
Shallow Depth and Pressure on watchOS
VMNet on macOS
Sensitive Content Analysis on iOS, iPadOS, and macOS
In addition, TestFlight now supports visionOS apps for internal and external testing, as well as testing iOS and iPadOS apps on visionOS.
Known issues
TestFlight Internal Only distribution from Xcode is not yet supported.
Xcode TestFlight
July 11, 2023
App Store Connect 1.12.1 is now available on the App Store. This update includes stability improvements and bug fixes.
App Store Connect for iPhone and iPad
July 6, 2023
You can now submit apps built with Xcode 15 beta 3 using the SDK for iOS 17 beta 3, iPadOS 17 beta 3, visionOS 1 beta, macOS 14 beta 3, tvOS 17 beta 3, and watchOS 10 beta 3 for internal and external testing, with support for the following:
Tap to Present ID on iOS
Apple Pay Later Merchandising on iOS and iPadOS
Shallow Depth and Pressure on watchOS
VMNet on macOS
Sensitive Content Analysis on iOS, iPadOS, and macOS
In addition, TestFlight now supports visionOS apps for internal and external testing, as well as testing iOS and iPadOS apps on visionOS.
Known issues
TestFlight Internal Only distribution from Xcode is not yet supported.
Xcode TestFlight
June 23, 2023
App Store Connect 1.12 is now available on the App Store. This update includes support for visionOS apps, stability improvements, and bug fixes.
App Store Connect for iPhone and iPad
June 21, 2023
You can now submit apps built with Xcode 15 beta 2 using the SDK for iOS 17 beta 2, iPadOS 17 beta 2, visionOS 1 beta, macOS 14 beta 2, tvOS 17 beta 2, and watchOS 10 beta 2 for internal and external testing, with support for the following:
Tap to Present ID on iOS
Apple Pay Later Merchandising on iOS and iPadOS
Shallow Depth and Pressure on watchOS
VMNet on macOS
Sensitive Content Analysis on iOS, iPadOS, and macOS
In addition, TestFlight now supports visionOS apps for internal and external testing, as well as testing iOS and iPadOS apps on visionOS.
Known issues
TestFlight Internal Only distribution from Xcode is not yet supported.
watchOS apps that have a minimum deployment target below 6.0 may not install properly with TestFlight. To resolve this, temporarily set the minimum deployment target to 6.0 or higher.
Xcode TestFlight
June 5, 2023
You can now submit apps built with Xcode 15 beta using the SDK for iOS 17 beta, iPadOS 17 beta, macOS 14 beta, tvOS 17 beta, and watchOS 10 beta for internal and external testing, with support for the following:
Tap to Present ID on iOS
Apple Pay Later Merchandising on iOS and iPadOS
Shallow Depth and Pressure on watchOS
VMNet on macOS
Sensitive Content Analysis on iOS, iPadOS, and macOS
Known issues
TestFlight Internal Only distribution from Xcode is not yet supported.
watchOS apps that have a minimum deployment target below version 6.0 may not install properly with TestFlight. To resolve this, temporarily set the minimum deployment target to version 6.0 or higher.
Xcode TestFlight
April 24, 2023
App Store Connect 1.11.2 is now available on the App Store for iPhone and iPad. This update includes stability improvements and bug fixes.
App Store Connect for iPhone and iPad
April 12, 2023
TestFlight 3.3.1 is now available on the App Store for iPhone, iPad, and Mac. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
March 21, 2023
Apps built for iOS 16.4 RC, iPadOS 16.4 RC, macOS 13.3 RC, tvOS 16.4 RC, or watchOS 9.4 RC using Xcode 14.3 RC can now be submitted to the App Store, and for internal and external testing through TestFlight.
Xcode TestFlight
March 17, 2023
You can now access a monthly
Transaction Tax Report
, which summarizes sales tax, use tax, goods and services tax, and other similar taxes applied on your transactions where Apple
administers
tax. This report is available for sales completed on the App Store in the United States and Canada.
Transaction Tax Report
March 15, 2023
Apps built for iOS 16.4 beta 4, iPadOS 16.4 bet 4, macOS 13.3 beta 4, tvOS 16.4 beta 4, or watchOS 9.4 beta 4 using Xcode 14.3 beta 3 can now be submitted for internal and external testing through TestFlight.
Xcode TestFlight
March 9, 2023
TestFlight 3.3 is now available on the App Store for iPhone, iPad, and Mac. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
February 28, 2023
Apps built for iOS 16.4 beta 2, iPadOS 16.4 beta 2, macOS 13.3 beta 2, tvOS 16.4 beta 2, or watchOS 9.4 beta 2 using Xcode 14.3 beta 2 can now be submitted for internal and external testing through TestFlight.
Xcode TestFlight
February 16, 2023
Apps built for iOS 16.4 beta, iPadOS 16.4 beta, macOS 13.3 beta, tvOS 16.4 beta, or watchOS 9.4 beta using Xcode 14.3 beta can now be submitted for internal and external testing through TestFlight.
Known issues
iOS and iPadOS apps that include App Intents are temporarily not supported.
Xcode TestFlight
February 14, 2023
TestFlight 3.2.4 is now available on the App Store for iPhone, iPad, and Mac. This update includes stability improvements and bug fixes.
TestFlight App Store Connect for iPhone and iPad
January 30, 2023
You can now choose to apply
Billing Grace Period
only to existing paid renewals or continue applying it to all subscription renewals (existing paid renewals and free offers transitioning to paid renewals). You can also choose a duration of 3, 16, or 28 days instead of having the duration predefined based on your subscription length. When you enable Billing Grace Period in App Store Connect, Apple attempts to address any subscriber billing issues and recover the subscription while subscribers retain access to subscription benefits. If the subscription is recovered within this period, there’s no interruption to the days of paid service or to your revenue.
Auto-renewable subscriptions
January 1, 2023
As of January 1, 2023, the euro (EUR) is the official currency of Croatia, replacing the Croatian kuna (HRK). Price tiers in App Store Connect have be converted to euros using the statutory fixed conversion rate of 7.53450 HRK = 1.00000 EUR established by the Council of the European Union on July 12, 2022.
View kuna and euro equivalent price tiers
The following has been automatically updated from kunas to euros:
Prices on the App Store (customers with auto-renewable subscriptions will be notified)
Proceeds from sales in Croatia
Sales and Trends reports and monthly financial reports for Croatia
Currency for your bank in App Store Connect, if you’ve selected the kuna
## Android 10 行为变更分析报告  
> 来源：官方文档《Android 10 行为变更：所有应用》

---

### 1. 版本信息
| 项目 | 内容 |
|---|---|
| 产品名称 | Android 10（API 29） |
| 版本号 | 10（API Level 29） |
| 发布日期 | 2019-09-03（官方发布日，文档提取时间 2025-09-02 为归档时间） |

---

### 2. 兼容性变化（破坏性变更一览）
| 变更类别 | 是否破坏性 | 影响范围 | 关键说明 |
|---|---|---|---|
| **非 SDK 接口限制** | ✅ 是 | 所有应用 | 继续收紧灰名单/黑名单，反射调用将抛出 `NoSuchFieldException / NoSuchMethodException` |
| **手势导航** | ✅ 是 | 所有应用 | 系统手势与 App 手势冲突时，默认由系统处理，可能导致返回键等失效 |
| **NDK 共享对象文本重定位** | ✅ 是 | 使用 native so 的应用 | SELinux 强制禁止，加载含文本重定位的 so 直接崩溃 |
| **Bionic 路径变化** | ✅ 是 | 读取 `/proc/self/maps` 或硬编码路径的 native 代码 | 真实路径变为 `/apex/...`，老路径仅保留符号链接 |
| **只执行内存映射** | ✅ 是 | 反作弊/加固/热修复框架 | 读取 `.text` 段会触发 `SIGSEGV` |
| **TLS 1.3 默认启用** | ⚠️ 部分 | 所有 HTTPS/TLS 连接 | ① 无法关闭 TLS 1.3 加密套件 ② 握手异常类型变化 ③ 不支持 0-RTT |
| **SHA-1 证书不再信任** | ✅ 是 | 服务器证书 | 连接使用 SHA-1 证书的网站直接失败 |
| **WLAN 直连广播粘性移除** | ✅ 是 | 使用 `WIFI_P2P_CONNECTION_CHANGED_ACTION` 等广播 | 注册时不再收到最后一次粘性广播 |
| **SYSTEM_ALERT_WINDOW 在 Go 设备禁用** | ✅ 是 | 悬浮窗权限 | Android 10 Go 版无法再申请或授予该权限 |
| **HTTPS 连接工厂 null 检查** | ✅ 是 | 调用 `setSSLSocketFactory(null)` | 抛出 `IllegalArgumentException` |
| **android.preference 弃用** | ⚠️ 未来 | 所有使用 Settings UI 的应用 | 编译期警告，运行期暂可用 |
| **ZIP 工具类行为变化** | ⚠️ 边缘 | 使用 `java.util.zip.*` | 异常类型/空 ZIP 处理逻辑改变 |
| **相机方向假设失效** | ⚠️ 是 | 相机预览/可折叠设备 | 纵向设备不再一定对应物理纵向 |
| **Android Beam 弃用** | ⚠️ 未来 | NFC 文件传输 | API 仍可用，但后续版本可能移除 |
| **BigDecimal / Regex / GradientDrawable 行为微调** | ⚠️ 边缘 | 使用对应 API | 结果顺序、默认值、异常类型变化 |

---

### 3. 对原有 App 的具体影响
1. **非 SDK 接口**  
   如果 App 通过反射访问 `hidden API`，在 Android 10 上可能直接崩溃。  
   **示例**：`ActivityManager.getService()` 反射调用在黑名单中将失败。

2. **手势导航冲突**  
   从屏幕边缘滑动手势被系统占用，侧滑菜单/返回手势可能无响应。  
   **示例**：侧滑抽屉需使用 `setSystemGestureExclusionRects()` 避开冲突区域。

3. **Native 库加载失败**  
   旧 so 若包含文本重定位，运行时报 `dlopen failed: DT_TEXTREL`。  
   **示例**：老版本 FFmpeg 2.x 编译的 so 需重新用 `-fPIC` 编译。

4. **HTTPS 连接异常**  
   - 服务器证书使用 SHA-1 → `SSLHandshakeException: CertPathValidatorException`  
   - 自定义 `SSLEngine` 逻辑未处理 `SSLProtocolException` → 崩溃。

5. **悬浮窗在 Go 设备失效**  
   调用 `Settings.canDrawOverlays()` 始终返回 `false`，导致悬浮球/浮窗功能被禁用。

6. **WLAN P2P 广播粘性移除**  
   注册广播后首次拿不到历史广播，导致初始化状态缺失。

7. **Settings UI 编译警告**  
   继续使用 `android.preference.*` 会出现 `deprecated` 警告，后续版本可能移除。

---

### 4. 开发者推荐行动清单
| 任务 | 优先级 | 操作指南 |
|---|---|---|
| **扫描非 SDK 接口** | P0 | 使用 `veridex` 工具或 Android Studio Lint → 列出灰/黑名单调用 → 迁移到官方 API 或申请新 API。 |
| **适配手势导航** | P0 | 在 `onWindowFocusChanged` 中调用 `Window#setSystemGestureExclusionRects()` 排除冲突区域；确保内容延伸至边缘（`WindowInsets`）。 |
| **重新编译 Native so** | P0 | 使用 NDK r21+ 重新编译，确保 `-fPIC` 且不含文本重定位；检查 `/proc/self/maps` 解析逻辑，支持 `/apex/...` 路径。 |
| **HTTPS/TLS 自检** | P1 | 检查服务器证书链，确保使用 SHA-256 及以上签名；测试 TLS 1.3 握手逻辑，捕获 `SSLProtocolException` 而非 `SSLHandshakeException`。 |
| **悬浮窗兼容 Go 设备** | P1 | 运行时检测 `ActivityManager.isLowRamDevice()`，在 Go 设备上隐藏悬浮窗功能或降级方案。 |
| **WLAN P2P 初始化** | P1 | 使用 `WifiP2pManager#requestConnectionInfo()` 等 `get()` 方法主动获取状态，替代粘性广播。 |
| **迁移 Settings UI** | P2 | 将 `android.preference.*` 替换为 `androidx.preference:preference:1.x.x`，参考官方迁移指南。 |
| **测试相机方向** | P2 | 在可折叠/旋转设备上测试，使用 `CameraCharacteristics.SENSOR_ORIENTATION` 动态计算方向，勿硬编码。 |
| **更新服务器加密套件** | P2 | 移除已废弃的 SHA-2 CBC 套件，优先使用 `TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384` 等 GCM 套件。 |

---

### 5. 一句话总结
**Android 10 的变更集中在隐私、安全与手势体验，开发者需立即检查非 SDK 接口、Native 库及 TLS 证书，并针对手势导航与 Go 设备做兼容性处理，以避免崩溃与功能缺失。**
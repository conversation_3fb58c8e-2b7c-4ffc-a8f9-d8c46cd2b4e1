## Android 10 行为变更影响分析报告

### 1. 版本信息
- **产品名称**：Android 10（API 29）
- **发布日期**：2019 年 9 月（官方发布时间）

---

### 2. 兼容性变化（破坏性变更汇总）

| 类别 | 具体变更 | 是否破坏向后兼容 |
|------|----------|------------------|
| **非 SDK 接口** | 新增/更新一批受限非 SDK 接口 | ✅ 可能直接崩溃 |
| **手势导航** | 边缘滑动默认被系统当作“返回” | ✅ 需适配手势冲突 |
| **NDK** | 共享对象禁止文本重定位（SELinux 强制） | ✅ 旧 so 直接无法加载 |
| **TLS/SSL** | 默认启用 TLS 1.3；不再信任 SHA-1 证书 | ✅ 连接失败 |
| **WLAN 广播** | 移除 `WIFI_P2P_*` 粘性广播 | ✅ 首次注册收不到 |
| **HTTPS 连接** | `setSSLSocketFactory(null)` 抛异常 | ✅ 运行时崩溃 |
| **UI 权限** | Go 设备禁止 `SYSTEM_ALERT_WINDOW` | ✅ 无法获取悬浮窗权限 |
| **序列化** | 默认 `serialVersionUID` 算法修正 | ✅ 反序列化失败 |
| **正则** | `String.split()` 零宽匹配行为变化 | ✅ 解析结果改变 |
| **加密套件** | 移除 6 组 SHA-2 CBC 套件 | ✅ 握手失败 |
| **摄像头** | 物理方向与显示方向解耦 | ✅ 取景器旋转/缩放异常 |
| **ZIP 工具** | `java.util.zip` 异常类型变化 | ✅ 需捕获新异常 |

---

### 3. 对原有 App 的具体影响

| 场景 | 可能症状 | 触发条件 |
|------|----------|----------|
| **反射/隐藏 API** | `NoSuchMethodError` / `ClassNotFoundException` | 使用了被限制的非 SDK 方法/字段 |
| **边缘滑动返回** | 自定义侧滑菜单/抽屉被系统手势拦截 | 未做 `WindowInsets`/`GestureDetector` 适配 |
| **Native so 加载** | `dlopen failed: text relocations` | so 含文本重定位且 `targetSdkVersion ≥ 29` |
| **HTTPS 请求** | `SSLHandshakeException: Certificate verify failed` | 服务器仍用 SHA-1 证书 |
| **P2P/Wi-Fi Direct** | 收不到 `WIFI_P2P_CONNECTION_CHANGED_ACTION` 广播 | 依赖粘性广播初始化逻辑 |
| **悬浮窗** | `Settings.canDrawOverlays()` 永远返回 false（Go 设备） | 新装/升级后首次申请权限 |
| **数据序列化** | `InvalidClassException: incompatible serialVersionUID` | 旧版本序列化数据在新版本读取 |
| **字符串分割** | 解析结果数组长度变化 | 使用零宽正则分割 |
| **加密通信** | `SSLHandshakeException: no cipher suites in common` | 服务器仅支持被移除的 CBC 套件 |
| **相机预览** | 画面旋转 90°/180° 或拉伸 | 未处理可折叠/旋转屏方向 |
| **ZIP 处理** | `NullPointerException` 代替 `IllegalStateException` | 调用 `Inflater.end()` 后继续使用 |
| **设置界面** | 使用已弃用的 `android.preference` | 编译期警告，未来移除 |

---

### 4. 开发者推荐行动清单

#### 立即执行（上线前必须）
1. **扫描非 SDK 接口**  
   - 使用 Google 提供的 [veridex 工具](https://developer.android.com/about/versions/10/non-sdk-q) 检测受限 API。  
   - 迁移到官方 SDK 或申请新的公开 API。

2. **测试手势冲突**  
   - 在 Android 10 真机/模拟器开启手势导航，验证边缘 20dp 区域手势。  
   - 使用 `setSystemGestureExclusionRects()` 排除冲突区域。

3. **检查 Native so**  
   - 运行 `readelf -a your.so | grep TEXTREL`，如有输出需重新编译。  
   - 升级 NDK 至 r21+ 并启用 `-fno-integrated-as -Wl,--no-warn-shared-textrel`。

4. **HTTPS 证书升级**  
   - 使用 SSL Labs 检测服务器证书链，确保不再使用 SHA-1。  
   - 如需兼容旧服务器，可临时 `SSLContext.getInstance("TLSv1.2")`。

5. **WLAN 广播初始化**  
   - 在 `onCreate()` 中主动调用 `WifiP2pManager.requestConnectionInfo()` 获取初始状态，而非依赖粘性广播。

#### 中期适配（建议 1-2 个版本内完成）
- **悬浮窗权限**  
  - 在 Go 设备上优雅降级，引导用户到系统设置或使用通知替代。
- **序列化兼容**  
  - 显式声明 `serialVersionUID` 或使用 `Parcelable`/`JSON` 替代 Java 序列化。
- **正则/字符串处理**  
  - 为 `split()` 结果添加单元测试，确保零宽匹配场景符合预期。
- **加密套件**  
  - 服务端升级支持 `TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384` 等 GCM 套件。
- **相机方向**  
  - 使用 `Camera2` API 并监听 `DisplayManager.DisplayListener` 动态调整预览方向。

#### 长期规划
- **迁移至 AndroidX Preference**  
  - 替换 `android.preference.*` 为 `androidx.preference:preference:1.2.0`。
- **移除 Android Beam**  
  - 改用 `Nearby Connections` 或 `Fast Share` 实现近场传输。
- **持续监控 tombstone**  
  - 在 CI 中加入只执行内存访问崩溃检测脚本，防止未来回归。

---

### 5. 一句话总结
> Android 10 在隐私、安全、手势导航三大方向引入多项强制变更，开发者需立即检测非 SDK 接口、升级证书、处理手势冲突并重新编译含文本重定位的 so，否则将出现崩溃或功能异常。
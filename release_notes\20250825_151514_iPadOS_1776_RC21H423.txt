Title: iPadOS 17.7.6 RC(21H423)
URL: https://developer.apple.com/machine-learning/
Extracted: 2025-08-25T15:15:14.233871
================================================================================

Foundation Models framework
Tap into the power of Apple Intelligence
You now have direct access to the
on-device
foundation model at the core of Apple Intelligence so you can build experiences that are smart, private, and work without internet connectivity through the Foundation Models framework. With native support for Swift, you can tap into the model with as few as three lines of code. You can use this framework to power intelligent features in your app, with model capabilities, such as text extraction, summarization, and more. Features like guided generation, tool calling, and more are built into the framework, making it easier than ever to implement intelligent experiences right into your existing apps and games.
Core ML
Fast and easy integration
Core ML delivers blazingly fast performance on Apple devices with easy integration of machine learning and AI models into your apps and games. Convert models from popular training libraries using Core ML Tools or download ready-to-use Core ML models. Easily preview models and understand their performance right in Xcode.
Speech
Advanced,
on-device
transcription
Take advantage of speech recognition and saliency features for a variety of languages. With the all-new SpeechAnalyzer, you can bring advanced,
on-device
transcription to your app.
Vision
Robust image and video analysis
Build powerful image and video analysis features with the latest in computer vision. New updates bring full-document text recognition and camera smudge detection to help elevate your app’s utilization of image analysis.
Machine-learning-powered APIs
Build more with
on-device models
Bring intelligent
on-device
machine-learning-powered features, natural language analysis, translation, and sound classification, to your app with just a few lines of code.
CreateML
Customize built-in system models
The Create ML app lets you quickly build and train Core ML models right on your Mac with no code. The easy-to-use app interface and ability to customize built-in system models make the process easier than ever, so all you need to get started is your training data.
Metal
Power the most advanced graphics workload
Metal puts the advanced capabilities of Apple-designed GPUs at your fingertips to power the most advanced graphics workloads. Now you can tap into machine learning capabilities like MetalFX, run inference networks directly in your shaders, and implement the latest neural rendering techniques with Metal 4.
Accelerate/BNNSGraph
Low Latency ML on the CPU
With the Accelerate framework and BNNSGraph API, you can optimize your ML workloads. When running real-time signal processing on the CPU, Accelerate’s BNNS Graph API provides strict latency and memory management control for your ML tasks.
MLX
An efficient array framework
MLX is an efficient array framework for numerical computing and machine learning on Apple silicon.
Experiment, train, and fine-tune open sourced models on your Mac, taking full advantage of Apple silicon’s unified memory.
Learn more
Machine Learning & AI
Create intelligent features and enable new experiences for your apps and games by leveraging powerful
on-device
machine learning. Learn how to build, use, train, and deploy machine learning and AI models for iPhone, iPad, Apple Vision Pro, Mac, and Apple Watch.
Get to know machine learning & AI
Foundation Models framework
Tap into the power of Apple Intelligence
More about this tile
Core ML
Fast and easy integration
More about this tile
Speech
Advanced,
on-device
transcription
More about this tile
Vision
Robust image and video analysis
More about this tile
Machine-learning-powered APIs
Build more with
on-device models
More about this tile
CreateML
Customize built-in system models
More about this tile
Metal
Power the most advanced graphics workloads
More about this tile
Accelerate/BNNSGraph
Low Latency ML on the CPU
More about this tile
MLX
An efficient array framework
More about this tile
What’s new in Machine Learning & AI
Dive into the latest key technologies and capabilities.
New to Machine Learning & AI development?
Check out the machine learning tutorials, an easy-to-navigate collection of resources to get started.
Developer stories
Play it again
The musical marvel Moises splits recorded songs into different parts.
Read more
Holding court
The SwingVision team harnesses machine learning to create a tennis app people love.
Read more
ML meets accessibility
Oko makes street crossings more accessible.
Read more
Meet with Apple
Sharpen your skills through
in-person
and online activities around the world.
Explore the schedule
Resources
Browse tools, documentation, sample code, videos, and more.
Learn more
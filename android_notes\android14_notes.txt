Android 14 行为变更
提取时间: 2025-09-02T11:09:31.652489
来源: https://developer.android.com/about/versions/14/behavior-changes-all?hl=zh-cn
================================================================================

此页面由

Cloud Translation API

翻译。

Android Developers

基本知识

行为变更：所有应用

使用集合让一切井井有条

根据您的偏好保存内容并对其进行分类。

Android 14 平台包含一些可能会影响您的应用的行为变更。以下行为变更将影响在 Android 14 上运行的

所有应用，无论采用哪种

targetSdkVersion

都不例外。您应该测试您的应用，然后根据需要进行修改，以适当地支持这些变更。

此外，请务必查看

仅影响以 Android 14 为目标平台的应用的行为变更

列表。

核心功能

默认拒绝设定精确的闹钟

Exact alarms are meant for user-intentioned notifications, or for actions that
need to happen at a precise time. Starting in Android 14, the

SCHEDULE_EXACT_ALARM

permission is

no longer being pre-granted to most newly installed apps
targeting Android 13 and higher

—the permission is denied by default.

Learn more about the

changes to the permission for scheduling exact
alarms

当应用进入缓存时，上下文注册的广播将加入队列

On Android 14, the system can

place context-registered broadcasts in a queue

while the app
is in the

cached state

. This is similar to the queuing
behavior that Android 12 (API level 31) introduced for async binder
transactions. Manifest-declared broadcasts aren't queued, and apps are removed
from the cached state for broadcast delivery.

When the app leaves the cached state, such as returning to the foreground, the
system delivers any queued broadcasts. Multiple instances of certain broadcasts
might be merged into one broadcast. Depending on other factors, such as system
health, apps might be removed from the cached state, and any previously queued
broadcasts are delivered.

应用只能终止自己的后台进程

从 Android 14 开始，当您的应用调用

killBackgroundProcesses()

时，该 API 只能终止您自己应用的后台进程。

如果您传入另一个应用的软件包名称，此方法对该应用的后台进程没有影响，并且 Logcat 中会显示以下消息：

Invalid packageName: com.example.anotherapp

您的应用不应使用

API，也不得以其他方式尝试影响其他应用的进程生命周期，即使在旧版操作系统上也是如此。Android 旨在让缓存应用在后台运行，并在系统需要内存时自动终止它们。如果您的应用会不必要地终止其他应用，则由于之后需要完全重启这些应用，因此可能会降低系统性能并增加耗电量，这比恢复现有缓存应用所消耗的资源要多得多。

对于第一个请求 MTU 的 GATT 客户端，MTU 设置为 517

Starting from Android 14, the Android Bluetooth stack more strictly adheres to

Version 5.2 of the Bluetooth Core Specification

and requests
the BLE ATT MTU to 517 bytes when the first GATT client requests an MTU using
the

BluetoothGatt#requestMtu(int)

API, and disregards all subsequent MTU
requests on that ACL connection.

To address this change and make your app more robust, consider the following
options:

Your peripheral device should respond to the Android device's MTU request
with a reasonable value that can be accommodated by the peripheral. The
final negotiated value will be a minimum of the Android requested value and
the remote provided value (for example,

min(517, remoteMtu)

Implementing this fix could require a firmware update for peripheral

Alternatively, limit your GATT characteristic writes based on the minimum
between the known supported value of your peripheral and the received MTU
change

A reminder that you should reduce 5 bytes from the supported size for
the headers

For example:

arrayMaxLength = min(SUPPORTED_MTU,
GATT_MAX_ATTR_LEN(517)) - 5

应用可被放入受限待机模式存储分区的新原因

Android 14 引入了一种可将应用放入

受限待机模式存储分区

的新原因。由于

onStartJob

onStopJob

onBind

方法超时，应用的作业多次触发 ANR 错误。（如需了解对

的更改，请参阅

JobScheduler 强化了回调和网络行为

如需跟踪应用是否已进入受限待机分桶，我们建议您在作业执行时使用 API

UsageStatsManager.getAppStandbyBucket()

进行日志记录，或在应用启动时使用

UsageStatsManager.queryEventsForSelf()

进行日志记录。

mlock 限制为 64 KB

In Android 14 (API level 34) and higher, the platform reduces the maximum memory
that can be locked using

mlock()

to 64 KB per process. In
previous versions, the limit was 64 MB per process. This restriction
promotes better memory management across apps and the system. To provide more
consistency across devices, Android 14 adds

a new CTS test

for the
new

limit on compatible devices.

系统强制执行缓存应用资源用量

By design

, an app's process is in a cached state when it's moved to the
background and no other app process components are running. Such an app process
is subject to being killed due to system memory pressure. Any work that

Activity

instances perform after the

onStop()

method has been called and
returned, while in this state, is unreliable and strongly discouraged.

Android 14 introduces consistency and enforcement to this design. Shortly after
an app process enters a cached state, background work is disallowed, until a
process component re-enters an active state of the lifecycle.

Apps that use typical framework-supported lifecycle APIs – such as

services

JobScheduler

, and

Jetpack WorkManager

– shouldn't be
impacted by these changes.

用户体验

关于不可关闭通知用户体验方式的变更

If your app shows non-dismissable foreground notifications to users, Android 14
has changed the behavior to allow users to dismiss such notifications.

This change applies to apps that prevent users from dismissing foreground
notifications by setting

Notification.FLAG_ONGOING_EVENT

through

Notification.Builder#setOngoing(true)

NotificationCompat.Builder#setOngoing(true)

. The behavior of

FLAG_ONGOING_EVENT

has changed to make such notifications actually
dismissable by the user.

These kinds of notifications are still non-dismissable in the following
conditions:

When the phone is locked

If the user selects a

Clear all

notification action (which helps with
accidental dismissals)

Also, this new behavior doesn't apply to notifications in the
following use cases:

CallStyle

notifications

Device policy controller (DPC) and supporting packages for enterprise

Media notifications

The default Search Selector package

数据安全信息更显眼

To enhance user privacy, Android 14 increases the number of places where the
system shows the information you have declared in the Play Console form.
Currently, users can view this information in the

Data safety

section on
your app's listing in Google Play.

We encourage you to review your app's location data sharing policies and take a
moment to make any applicable updates to your app's

Google Play Data safety
section

Learn more in the guide about how

data safety information is more visible

on Android 14.

无障碍

非线性字体放大至 200%

Starting in Android 14, the system supports font scaling up to 200%, providing
low-vision users with additional accessibility options that align with

Web
Content Accessibility Guidelines
(WCAG)

If you already use scaled pixels (sp) units to define text sizing, then this
change probably won't have a high impact on your app. However, you should

perform UI testing with the maximum font size enabled
(200%)

to
ensure that your app can accommodate larger font sizes without impacting
usability.

最低可安装的目标 API 级别

Starting with Android 14, apps with a

lower than 23
can't be installed. Requiring apps to meet these minimum target API level
requirements improves security and privacy for users.

Malware often targets older API levels in order to bypass security and privacy
protections that have been introduced in newer Android versions. For example,
some malware apps use a

of 22 to avoid being subjected to the
runtime permission model introduced in 2015 by Android 6.0 Marshmallow (API
level 23). This Android 14 change makes it harder for malware to avoid security
and privacy improvements.
Attempting to install an app targeting a lower API level will result in an
installation failure, with the following message appearing in Logcat:

INSTALL_FAILED_DEPRECATED_SDK_VERSION: App package must target at least SDK version 23, but found 7

On devices upgrading to Android 14, any apps with a

lower
than 23 will remain installed.

If you need to test an app targeting an older API level, use the following ADB
command:

adb install --bypass-low-target-sdk-block

FILENAME

.apk

媒体所有者软件包名称可能会隐去

媒体库支持查询

OWNER_PACKAGE_NAME

列，该列表示

存储特定媒体文件的应用

。从 Android 14 开始，除非满足以下条件之一，否则系统会隐去此值：

存储媒体文件的应用有一个软件包名称始终对其他应用可见。

查询媒体库的应用会请求

QUERY_ALL_PACKAGES

权限。

详细了解

Android 如何出于隐私保护目的而过滤软件包可见性

本页面上的内容和代码示例受

内容许可

部分所述许可的限制。Java 和 OpenJDK 是 Oracle 和/或其关联公司的注册商标。

最后更新时间 (UTC)：2025-08-27。

[[["易于理解","easyToUnderstand","thumb-up"],["解决了我的问题","solvedMyProblem","thumb-up"],["其他","otherUp","thumb-up"]],[["没有我需要的信息","missingTheInformationINeed","thumb-down"],["太复杂/步骤太多","tooComplicatedTooManySteps","thumb-down"],["内容需要更新","outOfDate","thumb-down"],["翻译问题","translationIssue","thumb-down"],["示例/代码问题","samplesCodeIssue","thumb-down"],["其他","otherDown","thumb-down"]],["最后更新时间 (UTC)：2025-08-27。"],[],[],null,[]]

[代码块] adb install --bypass-low-target-sdk-blockFILENAME.apk

[代码] adb install --bypass-low-target-sdk-blockFILENAME.apk

[命令] Your peripheral device should respond to the Android device's MTU request
with a reasonable value that can be accommodated by the peripheral. The
final negotiated value will be a minimum of the Android requested value and
the remote provided value (for example,min(517, remoteMtu))Implementing this fix could require a firmware update for peripheral

[命令] For example:arrayMaxLength = min(SUPPORTED_MTU,
GATT_MAX_ATTR_LEN(517)) - 5

[命令] In Android 14 (API level 34) and higher, the platform reduces the maximum memory
that can be locked usingmlock()to 64 KB per process. In
previous versions, the limit was 64 MB per process. This restriction
promotes better memory management across apps and the system. To provide more
consistency across devices, Android 14 addsa new CTS testfor the
newmlock()limit on compatible devices.

[命令] Apps that use typical framework-supported lifecycle APIs – such asservices,JobScheduler, andJetpack WorkManager– shouldn't be
impacted by these changes.

[命令] Malware often targets older API levels in order to bypass security and privacy
protections that have been introduced in newer Android versions. For example,
some malware apps use atargetSdkVersionof 22 to avoid being subjected to the
runtime permission model introduced in 2015 by Android 6.0 Marshmallow (API
level 23). This Android 14 change makes it harder for malware to avoid security
and privacy improvements.
Attempting to install an app targeting a lower API level will result in an
installation failure, with the following message appearing in Logcat:

[命令] adb install --bypass-low-target-sdk-blockFILENAME.apk
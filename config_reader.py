import yaml
## 配置读取器
class config_reader:
    def __init__(self, config_file):
        self.config_file = config_file
        self.config = self._read_config()
    def _read_config(self):
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError :
            print(f"读取配置不存在: {self.config_file}")
            return {}
        except yaml.YAMLError as e:
            print(f"YAML解析错误: {e}")
            return {}